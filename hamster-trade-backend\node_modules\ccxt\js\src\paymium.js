// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code
// EDIT THE CORRESPONDENT .ts FILE INSTEAD

//  ---------------------------------------------------------------------------
import Exchange from './abstract/paymium.js';
import { ExchangeError } from './base/errors.js';
import { Precise } from './base/Precise.js';
import { TICK_SIZE } from './base/functions/number.js';
import { sha256 } from './static_dependencies/noble-hashes/sha256.js';
//  ---------------------------------------------------------------------------
/**
 * @class paymium
 * @augments Exchange
 */
export default class paymium extends Exchange {
    describe() {
        return this.deepExtend(super.describe(), {
            'id': 'paymium',
            'name': 'Paymium',
            'countries': ['FR', 'EU'],
            'rateLimit': 2000,
            'version': 'v1',
            'has': {
                'CORS': true,
                'spot': true,
                'margin': undefined,
                'swap': false,
                'future': false,
                'option': false,
                'cancelOrder': true,
                'createDepositAddress': true,
                'createOrder': true,
                'fetchBalance': true,
                'fetchDepositAddress': true,
                'fetchDepositAddresses': true,
                'fetchDepositAddressesByNetwork': false,
                'fetchFundingHistory': false,
                'fetchFundingRate': false,
                'fetchFundingRateHistory': false,
                'fetchFundingRates': false,
                'fetchIndexOHLCV': false,
                'fetchMarkOHLCV': false,
                'fetchOpenInterestHistory': false,
                'fetchOrderBook': true,
                'fetchPremiumIndexOHLCV': false,
                'fetchTicker': true,
                'fetchTrades': true,
                'fetchTradingFee': false,
                'fetchTradingFees': false,
                'transfer': true,
            },
            'urls': {
                'logo': 'https://user-images.githubusercontent.com/51840849/87153930-f0f02200-c2c0-11ea-9c0a-40337375ae89.jpg',
                'api': {
                    'rest': 'https://paymium.com/api',
                },
                'www': 'https://www.paymium.com',
                'fees': 'https://www.paymium.com/page/help/fees',
                'doc': [
                    'https://github.com/Paymium/api-documentation',
                    'https://www.paymium.com/page/developers',
                    'https://paymium.github.io/api-documentation/',
                ],
                'referral': 'https://www.paymium.com/page/sign-up?referral=eDAzPoRQFMvaAB8sf-qj',
            },
            'api': {
                'public': {
                    'get': [
                        'countries',
                        'currencies',
                        'data/{currency}/ticker',
                        'data/{currency}/trades',
                        'data/{currency}/depth',
                        'bitcoin_charts/{id}/trades',
                        'bitcoin_charts/{id}/depth',
                    ],
                },
                'private': {
                    'get': [
                        'user',
                        'user/addresses',
                        'user/addresses/{address}',
                        'user/orders',
                        'user/orders/{uuid}',
                        'user/price_alerts',
                        'merchant/get_payment/{uuid}',
                    ],
                    'post': [
                        'user/addresses',
                        'user/orders',
                        'user/withdrawals',
                        'user/email_transfers',
                        'user/payment_requests',
                        'user/price_alerts',
                        'merchant/create_payment',
                    ],
                    'delete': [
                        'user/orders/{uuid}',
                        'user/orders/{uuid}/cancel',
                        'user/price_alerts/{id}',
                    ],
                },
            },
            'markets': {
                'BTC/EUR': this.safeMarketStructure({ 'id': 'eur', 'symbol': 'BTC/EUR', 'base': 'BTC', 'quote': 'EUR', 'baseId': 'btc', 'quoteId': 'eur', 'type': 'spot', 'spot': true }),
            },
            'fees': {
                'trading': {
                    'maker': this.parseNumber('-0.001'),
                    'taker': this.parseNumber('0.005'),
                },
            },
            'precisionMode': TICK_SIZE,
            'features': {
                'spot': {
                    'sandbox': false,
                    'createOrder': {
                        'marginMode': false,
                        'triggerPrice': false,
                        'triggerDirection': false,
                        'triggerPriceType': undefined,
                        'stopLossPrice': false,
                        'takeProfitPrice': false,
                        'attachedStopLossTakeProfit': undefined,
                        'timeInForce': {
                            'IOC': false,
                            'FOK': false,
                            'PO': false,
                            'GTD': false,
                        },
                        'hedged': false,
                        'trailing': false,
                        'leverage': false,
                        'marketBuyByCost': true,
                        'marketBuyRequiresPrice': false,
                        'selfTradePrevention': false,
                        'iceberg': false,
                    },
                    'createOrders': undefined,
                    'fetchMyTrades': undefined,
                    'fetchOrder': undefined,
                    'fetchOpenOrders': undefined,
                    'fetchOrders': undefined,
                    'fetchClosedOrders': undefined,
                    'fetchOHLCV': undefined, // todo
                },
                'swap': {
                    'linear': undefined,
                    'inverse': undefined,
                },
                'future': {
                    'linear': undefined,
                    'inverse': undefined,
                },
            },
        });
    }
    parseBalance(response) {
        const result = { 'info': response };
        const currencies = Object.keys(this.currencies);
        for (let i = 0; i < currencies.length; i++) {
            const code = currencies[i];
            const currency = this.currency(code);
            const currencyId = currency['id'];
            const free = 'balance_' + currencyId;
            if (free in response) {
                const account = this.account();
                const used = 'locked_' + currencyId;
                account['free'] = this.safeString(response, free);
                account['used'] = this.safeString(response, used);
                result[code] = account;
            }
        }
        return this.safeBalance(result);
    }
    /**
     * @method
     * @name paymium#fetchBalance
     * @description query for balance and get the amount of funds available for trading or funds locked in orders
     * @see https://paymium.github.io/api-documentation/#tag/User/paths/~1user/get
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
     */
    async fetchBalance(params = {}) {
        await this.loadMarkets();
        const response = await this.privateGetUser(params);
        return this.parseBalance(response);
    }
    /**
     * @method
     * @name paymium#fetchOrderBook
     * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
     * @see https://paymium.github.io/api-documentation/#tag/Public-data/paths/~1data~1%7Bcurrency%7D~1depth/get
     * @param {string} symbol unified symbol of the market to fetch the order book for
     * @param {int} [limit] the maximum amount of order book entries to return
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    async fetchOrderBook(symbol, limit = undefined, params = {}) {
        await this.loadMarkets();
        const market = this.market(symbol);
        const request = {
            'currency': market['id'],
        };
        const response = await this.publicGetDataCurrencyDepth(this.extend(request, params));
        return this.parseOrderBook(response, market['symbol'], undefined, 'bids', 'asks', 'price', 'amount');
    }
    parseTicker(ticker, market = undefined) {
        //
        // {
        //     "high":"33740.82",
        //     "low":"32185.15",
        //     "volume":"4.7890433",
        //     "bid":"33313.53",
        //     "ask":"33497.97",
        //     "midpoint":"33405.75",
        //     "vwap":"32802.5263553",
        //     "at":1643381654,
        //     "price":"33143.91",
        //     "open":"33116.86",
        //     "variation":"0.0817",
        //     "currency":"EUR",
        //     "trade_id":"ce2f5152-3ac5-412d-9b24-9fa72338474c",
        //     "size":"0.00041087"
        // }
        //
        const symbol = this.safeSymbol(undefined, market);
        const timestamp = this.safeTimestamp(ticker, 'at');
        const vwap = this.safeString(ticker, 'vwap');
        const baseVolume = this.safeString(ticker, 'volume');
        const quoteVolume = Precise.stringMul(baseVolume, vwap);
        const last = this.safeString(ticker, 'price');
        return this.safeTicker({
            'symbol': symbol,
            'timestamp': timestamp,
            'datetime': this.iso8601(timestamp),
            'high': this.safeString(ticker, 'high'),
            'low': this.safeString(ticker, 'low'),
            'bid': this.safeString(ticker, 'bid'),
            'bidVolume': undefined,
            'ask': this.safeString(ticker, 'ask'),
            'askVolume': undefined,
            'vwap': vwap,
            'open': this.safeString(ticker, 'open'),
            'close': last,
            'last': last,
            'previousClose': undefined,
            'change': undefined,
            'percentage': this.safeString(ticker, 'variation'),
            'average': undefined,
            'baseVolume': baseVolume,
            'quoteVolume': quoteVolume,
            'info': ticker,
        }, market);
    }
    /**
     * @method
     * @name paymium#fetchTicker
     * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
     * @see https://paymium.github.io/api-documentation/#tag/Public-data/paths/~1data~1%7Bcurrency%7D~1ticker/get
     * @param {string} symbol unified symbol of the market to fetch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    async fetchTicker(symbol, params = {}) {
        await this.loadMarkets();
        const market = this.market(symbol);
        const request = {
            'currency': market['id'],
        };
        const ticker = await this.publicGetDataCurrencyTicker(this.extend(request, params));
        //
        // {
        //     "high":"33740.82",
        //     "low":"32185.15",
        //     "volume":"4.7890433",
        //     "bid":"33313.53",
        //     "ask":"33497.97",
        //     "midpoint":"33405.75",
        //     "vwap":"32802.5263553",
        //     "at":1643381654,
        //     "price":"33143.91",
        //     "open":"33116.86",
        //     "variation":"0.0817",
        //     "currency":"EUR",
        //     "trade_id":"ce2f5152-3ac5-412d-9b24-9fa72338474c",
        //     "size":"0.00041087"
        // }
        //
        return this.parseTicker(ticker, market);
    }
    parseTrade(trade, market = undefined) {
        const timestamp = this.safeTimestamp(trade, 'created_at_int');
        const id = this.safeString(trade, 'uuid');
        market = this.safeMarket(undefined, market);
        const side = this.safeString(trade, 'side');
        const price = this.safeString(trade, 'price');
        const amountField = 'traded_' + market['base'].toLowerCase();
        const amount = this.safeString(trade, amountField);
        return this.safeTrade({
            'info': trade,
            'id': id,
            'order': undefined,
            'timestamp': timestamp,
            'datetime': this.iso8601(timestamp),
            'symbol': market['symbol'],
            'type': undefined,
            'side': side,
            'takerOrMaker': undefined,
            'price': price,
            'amount': amount,
            'cost': undefined,
            'fee': undefined,
        }, market);
    }
    /**
     * @method
     * @name paymium#fetchTrades
     * @description get the list of most recent trades for a particular symbol
     * @see https://paymium.github.io/api-documentation/#tag/Public-data/paths/~1data~1%7Bcurrency%7D~1trades/get
     * @param {string} symbol unified symbol of the market to fetch trades for
     * @param {int} [since] timestamp in ms of the earliest trade to fetch
     * @param {int} [limit] the maximum amount of trades to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
     */
    async fetchTrades(symbol, since = undefined, limit = undefined, params = {}) {
        await this.loadMarkets();
        const market = this.market(symbol);
        const request = {
            'currency': market['id'],
        };
        const response = await this.publicGetDataCurrencyTrades(this.extend(request, params));
        return this.parseTrades(response, market, since, limit);
    }
    /**
     * @method
     * @name paymium#createDepositAddress
     * @description create a currency deposit address
     * @see https://paymium.github.io/api-documentation/#tag/User/paths/~1user~1addresses/post
     * @param {string} code unified currency code of the currency for the deposit address
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
     */
    async createDepositAddress(code, params = {}) {
        await this.loadMarkets();
        const response = await this.privatePostUserAddresses(params);
        //
        //     {
        //         "address": "**********************************",
        //         "valid_until": **********,
        //         "currency": "BTC",
        //         "label": "Savings"
        //     }
        //
        return this.parseDepositAddress(response);
    }
    /**
     * @method
     * @name paymium#fetchDepositAddress
     * @description fetch the deposit address for a currency associated with this account
     * @see https://paymium.github.io/api-documentation/#tag/User/paths/~1user~1addresses~1%7Baddress%7D/get
     * @param {string} code unified currency code
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
     */
    async fetchDepositAddress(code, params = {}) {
        await this.loadMarkets();
        const request = {
            'address': code,
        };
        const response = await this.privateGetUserAddressesAddress(this.extend(request, params));
        //
        //     {
        //         "address": "**********************************",
        //         "valid_until": **********,
        //         "currency": "BTC",
        //         "label": "Savings"
        //     }
        //
        return this.parseDepositAddress(response);
    }
    /**
     * @method
     * @name paymium#fetchDepositAddresses
     * @description fetch deposit addresses for multiple currencies and chain types
     * @see https://paymium.github.io/api-documentation/#tag/User/paths/~1user~1addresses/get
     * @param {string[]|undefined} codes list of unified currency codes, default is undefined
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a list of [address structures]{@link https://docs.ccxt.com/#/?id=address-structure}
     */
    async fetchDepositAddresses(codes = undefined, params = {}) {
        await this.loadMarkets();
        const response = await this.privateGetUserAddresses(params);
        //
        //     [
        //         {
        //             "address": "**********************************",
        //             "valid_until": **********,
        //             "currency": "BTC",
        //             "label": "Savings"
        //         }
        //     ]
        //
        return this.parseDepositAddresses(response, codes);
    }
    parseDepositAddress(depositAddress, currency = undefined) {
        //
        //     {
        //         "address": "**********************************",
        //         "valid_until": **********,
        //         "currency": "BTC",
        //         "label": "Savings"
        //     }
        //
        const address = this.safeString(depositAddress, 'address');
        const currencyId = this.safeString(depositAddress, 'currency');
        return {
            'info': depositAddress,
            'currency': this.safeCurrencyCode(currencyId, currency),
            'network': undefined,
            'address': address,
            'tag': undefined,
        };
    }
    /**
     * @method
     * @name paymium#createOrder
     * @description create a trade order
     * @see https://paymium.github.io/api-documentation/#tag/Order/paths/~1user~1orders/post
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {string} type 'market' or 'limit'
     * @param {string} side 'buy' or 'sell'
     * @param {float} amount how much of currency you want to trade in units of base currency
     * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    async createOrder(symbol, type, side, amount, price = undefined, params = {}) {
        await this.loadMarkets();
        const market = this.market(symbol);
        const request = {
            'type': this.capitalize(type) + 'Order',
            'currency': market['id'],
            'direction': side,
            'amount': amount,
        };
        if (type !== 'market') {
            request['price'] = price;
        }
        const response = await this.privatePostUserOrders(this.extend(request, params));
        return this.safeOrder({
            'info': response,
            'id': response['uuid'],
        }, market);
    }
    /**
     * @method
     * @name paymium#cancelOrder
     * @description cancels an open order
     * @see https://paymium.github.io/api-documentation/#tag/Order/paths/~1user~1orders~1%7Buuid%7D/delete
     * @see https://paymium.github.io/api-documentation/#tag/Order/paths/~1user~1orders~1%7Buuid%7D~1cancel/delete
     * @param {string} id order id
     * @param {string} symbol not used by paymium cancelOrder ()
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    async cancelOrder(id, symbol = undefined, params = {}) {
        const request = {
            'uuid': id,
        };
        const response = await this.privateDeleteUserOrdersUuidCancel(this.extend(request, params));
        return this.safeOrder({
            'info': response,
        });
    }
    /**
     * @method
     * @name paymium#transfer
     * @description transfer currency internally between wallets on the same account
     * @see https://paymium.github.io/api-documentation/#tag/Transfer/paths/~1user~1email_transfers/post
     * @param {string} code unified currency code
     * @param {float} amount amount to transfer
     * @param {string} fromAccount account to transfer from
     * @param {string} toAccount account to transfer to
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [transfer structure]{@link https://docs.ccxt.com/#/?id=transfer-structure}
     */
    async transfer(code, amount, fromAccount, toAccount, params = {}) {
        await this.loadMarkets();
        const currency = this.currency(code);
        if (toAccount.indexOf('@') < 0) {
            throw new ExchangeError(this.id + ' transfer() only allows transfers to an email address');
        }
        if (code !== 'BTC' && code !== 'EUR') {
            throw new ExchangeError(this.id + ' transfer() only allows BTC or EUR');
        }
        const request = {
            'currency': currency['id'],
            'amount': this.currencyToPrecision(code, amount),
            'email': toAccount,
            // 'comment': 'a small note explaining the transfer'
        };
        const response = await this.privatePostUserEmailTransfers(this.extend(request, params));
        //
        //     {
        //         "uuid": "968f4580-e26c-4ad8-8bcd-874d23d55296",
        //         "type": "Transfer",
        //         "currency": "BTC",
        //         "currency_amount": "string",
        //         "created_at": "2013-10-24T10:34:37.000Z",
        //         "updated_at": "2013-10-24T10:34:37.000Z",
        //         "amount": "1.0",
        //         "state": "executed",
        //         "currency_fee": "0.0",
        //         "btc_fee": "0.0",
        //         "comment": "string",
        //         "traded_btc": "string",
        //         "traded_currency": "string",
        //         "direction": "buy",
        //         "price": "string",
        //         "account_operations": [
        //             {
        //                 "uuid": "968f4580-e26c-4ad8-8bcd-874d23d55296",
        //                 "amount": "1.0",
        //                 "currency": "BTC",
        //                 "created_at": "2013-10-24T10:34:37.000Z",
        //                 "created_at_int": **********,
        //                 "name": "account_operation",
        //                 "address": "**********************************",
        //                 "tx_hash": "string",
        //                 "is_trading_account": true
        //             }
        //         ]
        //     }
        //
        return this.parseTransfer(response, currency);
    }
    parseTransfer(transfer, currency = undefined) {
        //
        //     {
        //         "uuid": "968f4580-e26c-4ad8-8bcd-874d23d55296",
        //         "type": "Transfer",
        //         "currency": "BTC",
        //         "currency_amount": "string",
        //         "created_at": "2013-10-24T10:34:37.000Z",
        //         "updated_at": "2013-10-24T10:34:37.000Z",
        //         "amount": "1.0",
        //         "state": "executed",
        //         "currency_fee": "0.0",
        //         "btc_fee": "0.0",
        //         "comment": "string",
        //         "traded_btc": "string",
        //         "traded_currency": "string",
        //         "direction": "buy",
        //         "price": "string",
        //         "account_operations": [
        //             {
        //                 "uuid": "968f4580-e26c-4ad8-8bcd-874d23d55296",
        //                 "amount": "1.0",
        //                 "currency": "BTC",
        //                 "created_at": "2013-10-24T10:34:37.000Z",
        //                 "created_at_int": **********,
        //                 "name": "account_operation",
        //                 "address": "**********************************",
        //                 "tx_hash": "string",
        //                 "is_trading_account": true
        //             }
        //         ]
        //     }
        //
        const currencyId = this.safeString(transfer, 'currency');
        const updatedAt = this.safeString(transfer, 'updated_at');
        const timetstamp = this.parseDate(updatedAt);
        const accountOperations = this.safeValue(transfer, 'account_operations');
        const firstOperation = this.safeValue(accountOperations, 0, {});
        const status = this.safeString(transfer, 'state');
        return {
            'info': transfer,
            'id': this.safeString(transfer, 'uuid'),
            'timestamp': timetstamp,
            'datetime': this.iso8601(timetstamp),
            'currency': this.safeCurrencyCode(currencyId, currency),
            'amount': this.safeNumber(transfer, 'amount'),
            'fromAccount': undefined,
            'toAccount': this.safeString(firstOperation, 'address'),
            'status': this.parseTransferStatus(status),
        };
    }
    parseTransferStatus(status) {
        const statuses = {
            'executed': 'ok',
            // what are the other statuses?
        };
        return this.safeString(statuses, status, status);
    }
    sign(path, api = 'public', method = 'GET', params = {}, headers = undefined, body = undefined) {
        let url = this.urls['api']['rest'] + '/' + this.version + '/' + this.implodeParams(path, params);
        const query = this.omit(params, this.extractParams(path));
        if (api === 'public') {
            if (Object.keys(query).length) {
                url += '?' + this.urlencode(query);
            }
        }
        else {
            this.checkRequiredCredentials();
            const nonce = this.nonce().toString();
            let auth = nonce + url;
            headers = {
                'Api-Key': this.apiKey,
                'Api-Nonce': nonce,
            };
            if (method === 'POST') {
                if (Object.keys(query).length) {
                    body = this.json(query);
                    auth += body;
                    headers['Content-Type'] = 'application/json';
                }
            }
            else {
                if (Object.keys(query).length) {
                    const queryString = this.urlencode(query);
                    auth += queryString;
                    url += '?' + queryString;
                }
            }
            headers['Api-Signature'] = this.hmac(this.encode(auth), this.encode(this.secret), sha256);
        }
        return { 'url': url, 'method': method, 'body': body, 'headers': headers };
    }
    handleErrors(httpCode, reason, url, method, headers, body, response, requestHeaders, requestBody) {
        if (response === undefined) {
            return undefined;
        }
        const errors = this.safeValue(response, 'errors');
        if (errors !== undefined) {
            throw new ExchangeError(this.id + ' ' + this.json(response));
        }
        return undefined;
    }
}
