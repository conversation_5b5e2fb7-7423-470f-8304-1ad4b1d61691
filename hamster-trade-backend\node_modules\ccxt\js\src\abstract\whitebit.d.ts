import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';
interface Exchange {
    webGetV1Healthcheck(params?: {}): Promise<implicitReturnType>;
    v1PublicGetMarkets(params?: {}): Promise<implicitReturnType>;
    v1PublicGetTickers(params?: {}): Promise<implicitReturnType>;
    v1PublicGetTicker(params?: {}): Promise<implicitReturnType>;
    v1PublicGetSymbols(params?: {}): Promise<implicitReturnType>;
    v1PublicGetDepthResult(params?: {}): Promise<implicitReturnType>;
    v1PublicGetHistory(params?: {}): Promise<implicitReturnType>;
    v1PublicGetKline(params?: {}): Promise<implicitReturnType>;
    v1PrivatePostAccountBalance(params?: {}): Promise<implicitReturnType>;
    v1PrivatePostOrderNew(params?: {}): Promise<implicitReturnType>;
    v1PrivatePostOrderCancel(params?: {}): Promise<implicitReturnType>;
    v1PrivatePostOrders(params?: {}): Promise<implicitReturnType>;
    v1PrivatePostAccountOrderHistory(params?: {}): Promise<implicitReturnType>;
    v1PrivatePostAccountExecutedHistory(params?: {}): Promise<implicitReturnType>;
    v1PrivatePostAccountExecutedHistoryAll(params?: {}): Promise<implicitReturnType>;
    v1PrivatePostAccountOrder(params?: {}): Promise<implicitReturnType>;
    v2PublicGetMarkets(params?: {}): Promise<implicitReturnType>;
    v2PublicGetTicker(params?: {}): Promise<implicitReturnType>;
    v2PublicGetAssets(params?: {}): Promise<implicitReturnType>;
    v2PublicGetFee(params?: {}): Promise<implicitReturnType>;
    v2PublicGetDepthMarket(params?: {}): Promise<implicitReturnType>;
    v2PublicGetTradesMarket(params?: {}): Promise<implicitReturnType>;
    v4PublicGetAssets(params?: {}): Promise<implicitReturnType>;
    v4PublicGetCollateralMarkets(params?: {}): Promise<implicitReturnType>;
    v4PublicGetFee(params?: {}): Promise<implicitReturnType>;
    v4PublicGetOrderbookDepthMarket(params?: {}): Promise<implicitReturnType>;
    v4PublicGetOrderbookMarket(params?: {}): Promise<implicitReturnType>;
    v4PublicGetTicker(params?: {}): Promise<implicitReturnType>;
    v4PublicGetTradesMarket(params?: {}): Promise<implicitReturnType>;
    v4PublicGetTime(params?: {}): Promise<implicitReturnType>;
    v4PublicGetPing(params?: {}): Promise<implicitReturnType>;
    v4PublicGetMarkets(params?: {}): Promise<implicitReturnType>;
    v4PublicGetFutures(params?: {}): Promise<implicitReturnType>;
    v4PublicGetPlatformStatus(params?: {}): Promise<implicitReturnType>;
    v4PublicGetMiningPool(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostCollateralAccountBalance(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostCollateralAccountBalanceSummary(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostCollateralAccountPositionsHistory(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostCollateralAccountLeverage(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostCollateralAccountPositionsOpen(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostCollateralAccountSummary(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostCollateralAccountFundingHistory(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountAddress(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountBalance(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountCreateNewAddress(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountCodes(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountCodesApply(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountCodesMy(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountCodesHistory(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountFiatDepositUrl(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountHistory(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountWithdraw(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountWithdrawPay(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountTransfer(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountSmartPlans(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountSmartInvestment(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountSmartInvestmentClose(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountSmartInvestments(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountFee(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMainAccountSmartInterestPaymentHistory(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostTradeAccountBalance(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostTradeAccountExecutedHistory(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostTradeAccountOrder(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostTradeAccountOrderHistory(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderCollateralLimit(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderCollateralMarket(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderCollateralStopLimit(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderCollateralTriggerMarket(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderCollateralBulk(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderNew(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderMarket(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderStockMarket(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderStopLimit(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderStopMarket(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderCancel(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderCancelAll(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderKillSwitch(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderKillSwitchStatus(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderBulk(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderModify(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderConditionalCancel(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrders(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOcoOrders(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderCollateralOco(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderOcoCancel(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostOrderOtoCancel(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostProfileWebsocketToken(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostConvertEstimate(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostConvertConfirm(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostConvertHistory(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountCreate(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountDelete(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountEdit(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountList(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountTransfer(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountBlock(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountUnblock(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountBalances(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountTransferHistory(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountApiKeyCreate(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountApiKeyEdit(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountApiKeyDelete(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountApiKeyList(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountApiKeyReset(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountApiKeyIpAddressList(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountApiKeyIpAddressCreate(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostSubAccountApiKeyIpAddressDelete(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMiningRewards(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostMarketFee(params?: {}): Promise<implicitReturnType>;
    v4PrivatePostConditionalOrders(params?: {}): Promise<implicitReturnType>;
}
declare abstract class Exchange extends _Exchange {
}
export default Exchange;
