import { Routes, Route } from 'react-router-dom'
import { useEffect } from 'react'
import { useDispatch } from 'react-redux'
import { fetchExchanges } from './store/slices/marketSlice'
import { wsService } from './services/api'
import Layout from './components/Layout/Layout'
import Dashboard from './pages/Dashboard/Dashboard'
import Chart from './pages/Chart/Chart'
import Markets from './pages/Markets/Markets'
import './App.css'

function App() {
  const dispatch = useDispatch()

  useEffect(() => {
    // Initialize app
    dispatch(fetchExchanges())

    // Connect to WebSocket
    wsService.connect().catch(console.error)

    // Cleanup on unmount
    return () => {
      wsService.disconnect()
    }
  }, [dispatch])

  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/chart" element={<Chart />} />
        <Route path="/chart/:exchange/:symbol" element={<Chart />} />
        <Route path="/markets" element={<Markets />} />
        <Route path="/markets/:exchange" element={<Markets />} />
      </Routes>
    </Layout>
  )
}

export default App
