.search-bar {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #1a1a1a;
  border: 2px solid #333;
  border-radius: 12px;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.search-input-container.focused {
  border-color: #4ade80;
  box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.1);
}

.search-icon {
  color: #9ca3af;
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

.search-input {
  flex: 1;
  background: none;
  border: none;
  color: #ffffff;
  font-size: 1rem;
  outline: none;
}

.search-input::placeholder {
  color: #6b7280;
}

.clear-button {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.25rem;
  margin-left: 0.5rem;
  border-radius: 4px;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.clear-button:hover {
  background-color: #333;
  color: #ffffff;
}

/* Responsive */
@media (max-width: 768px) {
  .search-input-container {
    padding: 0.6rem 0.8rem;
  }
  
  .search-input {
    font-size: 0.9rem;
  }
}
