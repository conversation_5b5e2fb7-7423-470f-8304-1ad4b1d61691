.dashboard {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  font-size: 2.5rem;
  margin: 0 0 0.5rem 0;
  color: #4ade80;
}

.dashboard-header p {
  color: #9ca3af;
  font-size: 1.1rem;
  margin: 0;
}

.search-section {
  margin-bottom: 3rem;
}

.search-results {
  margin-top: 1.5rem;
}

.search-results h3 {
  color: #ffffff;
  margin-bottom: 1rem;
}

.loading {
  color: #9ca3af;
  text-align: center;
  padding: 2rem;
}

.quick-actions {
  margin-bottom: 3rem;
}

.quick-actions h2 {
  color: #ffffff;
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

.action-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border: 1px solid #475569;
  border-radius: 12px;
  padding: 2rem;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: #4ade80;
}

.action-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.action-card h3 {
  color: #ffffff;
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
}

.action-card p {
  color: #9ca3af;
  margin: 0;
  line-height: 1.5;
}

.exchanges-section {
  margin-bottom: 3rem;
}

.exchanges-section h2 {
  color: #ffffff;
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

.exchanges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.popular-markets h2 {
  color: #ffffff;
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

.market-categories {
  background-color: #1a1a1a;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #333;
}

.category-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #333;
  padding-bottom: 1rem;
}

.tab {
  background: none;
  border: none;
  color: #9ca3af;
  padding: 0.5rem 1rem;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s;
}

.tab:hover {
  background-color: #2a2a2a;
  color: #ffffff;
}

.tab.active {
  background-color: #4ade80;
  color: #000000;
}

.popular-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.market-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #2a2a2a;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.market-item:hover {
  background-color: #333;
}

.symbol {
  font-weight: bold;
  color: #ffffff;
  min-width: 100px;
}

.price {
  color: #ffffff;
  font-family: 'Courier New', monospace;
  min-width: 120px;
  text-align: right;
}

.change {
  font-family: 'Courier New', monospace;
  min-width: 80px;
  text-align: right;
  font-weight: bold;
}

.change.positive {
  color: #4ade80;
}

.change.negative {
  color: #ef4444;
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard {
    padding: 1rem;
  }
  
  .dashboard-header h1 {
    font-size: 2rem;
  }
  
  .action-cards {
    grid-template-columns: 1fr;
  }
  
  .exchanges-grid {
    grid-template-columns: 1fr;
  }
  
  .category-tabs {
    flex-wrap: wrap;
  }
  
  .market-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .symbol, .price, .change {
    min-width: auto;
    text-align: left;
  }
}
