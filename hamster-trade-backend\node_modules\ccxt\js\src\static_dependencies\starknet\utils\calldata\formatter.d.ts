/**
 * Formats the given data based on the provided type definition.
 *
 * @param {any} data - The data to be formatted.
 * @param {any} type - The type definition for the data.
 * @param {any} [sameType] - The same type definition to be used (optional).
 * @returns - The formatted data.
 */
export default function formatter(data: Record<string, any>, type: Record<string, any>, sameType?: any): Record<string, any>;
