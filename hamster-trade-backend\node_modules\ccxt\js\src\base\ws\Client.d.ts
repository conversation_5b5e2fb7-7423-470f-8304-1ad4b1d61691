import { Future } from './Future.js';
import { Dictionary, Str } from '../types.js';
export default class Client {
    connected: Promise<any>;
    disconnected: ReturnType<typeof Future>;
    futures: Dictionary<any>;
    rejections: Dictionary<any>;
    keepAlive: number;
    connection: any;
    connectionTimeout: any;
    verbose: boolean;
    connectionTimer: any;
    lastPong: any;
    maxPingPongMisses: any;
    pingInterval: any;
    connectionEstablished: any;
    gunzip: any;
    error: any;
    inflate: any;
    url: string;
    isConnected: any;
    onConnectedCallback: any;
    onMessageCallback: any;
    onErrorCallback: any;
    onCloseCallback: any;
    ping: any;
    subscriptions: Dictionary<any>;
    throttle: any;
    constructor(url: string, onMessageCallback: Function | undefined, onErrorCallback: Function | undefined, onCloseCallback: Function | undefined, onConnectedCallback: Function | undefined, config?: {});
    future(messageHash: string): any;
    resolve(result: any, messageHash: Str): any;
    reject(result: any, messageHash?: Str): any;
    log(...args: any[]): void;
    connect(backoffDelay?: number): void;
    isOpen(): boolean;
    reset(error: any): void;
    onConnectionTimeout(): void;
    setConnectionTimeout(): void;
    clearConnectionTimeout(): void;
    setPingInterval(): void;
    clearPingInterval(): void;
    onPingInterval(): void;
    onOpen(): void;
    onPing(): void;
    onPong(): void;
    onError(error: any): void;
    onClose(event: any): void;
    onUpgrade(message: any): void;
    send(message: any): Promise<any>;
    close(): void;
    onMessage(messageEvent: any): void;
}
