import { Link } from 'react-router-dom'
import './ExchangeCard.css'

const ExchangeCard = ({ exchange }) => {
  const getExchangeIcon = (exchangeId) => {
    const icons = {
      binance: '🟡',
      okx: '⚫',
      bybit: '🟠',
      coinbase: '🔵',
      kraken: '🟣',
      huobi: '🔴',
      kucoin: '🟢',
    }
    return icons[exchangeId] || '🏪'
  }

  const getStatusColor = (status = 'connected') => {
    const colors = {
      connected: '#4ade80',
      connecting: '#fbbf24',
      disconnected: '#ef4444',
      error: '#ef4444',
    }
    return colors[status] || colors.disconnected
  }

  return (
    <div className="exchange-card">
      <div className="exchange-header">
        <div className="exchange-info">
          <div className="exchange-icon">
            {getExchangeIcon(exchange.id)}
          </div>
          <div className="exchange-details">
            <h3 className="exchange-name">{exchange.name}</h3>
            <p className="exchange-id">{exchange.id}</p>
          </div>
        </div>
        <div className="exchange-status">
          <div 
            className="status-dot"
            style={{ backgroundColor: getStatusColor('connected') }}
          ></div>
          <span className="status-text">Connected</span>
        </div>
      </div>

      <div className="exchange-stats">
        <div className="stat-item">
          <span className="stat-label">Markets</span>
          <span className="stat-value">1,200+</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">24h Volume</span>
          <span className="stat-value">$2.5B</span>
        </div>
      </div>

      <div className="exchange-countries">
        <span className="countries-label">Countries:</span>
        <span className="countries-list">
          {exchange.countries?.join(', ') || 'Global'}
        </span>
      </div>

      <div className="exchange-actions">
        <Link 
          to={`/markets/${exchange.id}`}
          className="action-button primary"
        >
          View Markets
        </Link>
        <a 
          href={exchange.urls?.www}
          target="_blank"
          rel="noopener noreferrer"
          className="action-button secondary"
        >
          Visit Site
        </a>
      </div>
    </div>
  )
}

export default ExchangeCard
