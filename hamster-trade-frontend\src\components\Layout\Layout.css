.layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #0d1421;
  color: #ffffff;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1rem;
  height: 60px;
  background-color: #1a1a1a;
  border-bottom: 1px solid #333;
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.sidebar-toggle:hover {
  background-color: #333;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0;
  color: #4ade80;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ef4444;
}

.status-indicator.connected {
  background-color: #4ade80;
}

.main-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar {
  width: 250px;
  background-color: #1a1a1a;
  border-right: 1px solid #333;
  transition: width 0.3s ease;
  overflow: hidden;
}

.sidebar.closed {
  width: 60px;
}

.sidebar-nav {
  padding: 1rem 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: #9ca3af;
  text-decoration: none;
  transition: all 0.2s;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background-color: #2a2a2a;
  color: #ffffff;
}

.nav-item.active {
  background-color: #1e40af;
  color: #ffffff;
  border-left-color: #4ade80;
}

.nav-icon {
  font-size: 1.2rem;
  min-width: 20px;
  text-align: center;
}

.nav-text {
  white-space: nowrap;
  overflow: hidden;
}

.main-content {
  flex: 1;
  overflow: auto;
  background-color: #0d1421;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    position: absolute;
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    z-index: 999;
    transform: translateX(-100%);
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .sidebar.closed {
    width: 250px;
    transform: translateX(-100%);
  }
  
  .main-content {
    margin-left: 0;
  }
}
