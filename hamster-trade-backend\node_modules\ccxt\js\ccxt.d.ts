import { Exchange } from './src/base/Exchange.js';
import { Precise } from './src/base/Precise.js';
import * as functions from './src/base/functions.js';
import * as errors from './src/base/errors.js';
import type { Int, int, Str, Strings, Num, Bool, IndexType, OrderSide, OrderType, MarketType, SubType, Dict, NullableDict, List, NullableList, Fee, OHLCV, OHLCVC, implicitReturnType, Market, Currency, Dictionary, MinMax, FeeInterface, TradingFeeInterface, MarketInterface, Trade, Order, OrderBook, Ticker, Transaction, Tickers, CurrencyInterface, Balance, BalanceAccount, Account, PartialBalances, Balances, DepositAddress, WithdrawalResponse, FundingRate, FundingRates, Position, BorrowInterest, LeverageTier, LedgerEntry, DepositWithdrawFeeNetwork, DepositWithdrawFee, TransferEntry, CrossBorrowRate, IsolatedBorrowRate, FundingRateHistory, OpenInterest, Liquidation, OrderRequest, CancellationRequest, FundingHistory, MarketMarginModes, MarginMode, Greeks, Conversion, Option, LastPrice, Leverage, MarginModification, Leverages, LastPrices, Currencies, TradingFees, MarginModes, OptionChain, IsolatedBorrowRates, CrossBorrowRates, LeverageTiers, LongShortRatio, OrderBooks, OpenInterests, ConstructorArgs } from './src/base/types.js';
import { BaseError, ExchangeError, AuthenticationError, PermissionDenied, AccountNotEnabled, AccountSuspended, ArgumentsRequired, BadRequest, BadSymbol, OperationRejected, NoChange, MarginModeAlreadySet, MarketClosed, ManualInteractionNeeded, InsufficientFunds, InvalidAddress, AddressPending, InvalidOrder, OrderNotFound, OrderNotCached, OrderImmediatelyFillable, OrderNotFillable, DuplicateOrderId, ContractUnavailable, NotSupported, InvalidProxySettings, ExchangeClosedByUser, OperationFailed, NetworkError, DDoSProtection, RateLimitExceeded, ExchangeNotAvailable, OnMaintenance, InvalidNonce, ChecksumError, RequestTimeout, BadResponse, NullResponse, CancelPending, UnsubscribeError } from './src/base/errors.js';
declare const version = "4.4.91";
import alpaca from './src/alpaca.js';
import apex from './src/apex.js';
import ascendex from './src/ascendex.js';
import bequant from './src/bequant.js';
import bigone from './src/bigone.js';
import binance from './src/binance.js';
import binancecoinm from './src/binancecoinm.js';
import binanceus from './src/binanceus.js';
import binanceusdm from './src/binanceusdm.js';
import bingx from './src/bingx.js';
import bit2c from './src/bit2c.js';
import bitbank from './src/bitbank.js';
import bitbns from './src/bitbns.js';
import bitfinex from './src/bitfinex.js';
import bitflyer from './src/bitflyer.js';
import bitget from './src/bitget.js';
import bithumb from './src/bithumb.js';
import bitmart from './src/bitmart.js';
import bitmex from './src/bitmex.js';
import bitopro from './src/bitopro.js';
import bitrue from './src/bitrue.js';
import bitso from './src/bitso.js';
import bitstamp from './src/bitstamp.js';
import bitteam from './src/bitteam.js';
import bittrade from './src/bittrade.js';
import bitvavo from './src/bitvavo.js';
import blockchaincom from './src/blockchaincom.js';
import blofin from './src/blofin.js';
import btcalpha from './src/btcalpha.js';
import btcbox from './src/btcbox.js';
import btcmarkets from './src/btcmarkets.js';
import btcturk from './src/btcturk.js';
import bybit from './src/bybit.js';
import cex from './src/cex.js';
import coinbase from './src/coinbase.js';
import coinbaseadvanced from './src/coinbaseadvanced.js';
import coinbaseexchange from './src/coinbaseexchange.js';
import coinbaseinternational from './src/coinbaseinternational.js';
import coincatch from './src/coincatch.js';
import coincheck from './src/coincheck.js';
import coinex from './src/coinex.js';
import coinmate from './src/coinmate.js';
import coinmetro from './src/coinmetro.js';
import coinone from './src/coinone.js';
import coinsph from './src/coinsph.js';
import coinspot from './src/coinspot.js';
import cryptocom from './src/cryptocom.js';
import cryptomus from './src/cryptomus.js';
import defx from './src/defx.js';
import delta from './src/delta.js';
import deribit from './src/deribit.js';
import derive from './src/derive.js';
import digifinex from './src/digifinex.js';
import ellipx from './src/ellipx.js';
import exmo from './src/exmo.js';
import fmfwio from './src/fmfwio.js';
import gate from './src/gate.js';
import gateio from './src/gateio.js';
import gemini from './src/gemini.js';
import hashkey from './src/hashkey.js';
import hitbtc from './src/hitbtc.js';
import hollaex from './src/hollaex.js';
import htx from './src/htx.js';
import huobi from './src/huobi.js';
import hyperliquid from './src/hyperliquid.js';
import independentreserve from './src/independentreserve.js';
import indodax from './src/indodax.js';
import kraken from './src/kraken.js';
import krakenfutures from './src/krakenfutures.js';
import kucoin from './src/kucoin.js';
import kucoinfutures from './src/kucoinfutures.js';
import latoken from './src/latoken.js';
import lbank from './src/lbank.js';
import luno from './src/luno.js';
import mercado from './src/mercado.js';
import mexc from './src/mexc.js';
import modetrade from './src/modetrade.js';
import myokx from './src/myokx.js';
import ndax from './src/ndax.js';
import novadax from './src/novadax.js';
import oceanex from './src/oceanex.js';
import okcoin from './src/okcoin.js';
import okx from './src/okx.js';
import okxus from './src/okxus.js';
import onetrading from './src/onetrading.js';
import oxfun from './src/oxfun.js';
import p2b from './src/p2b.js';
import paradex from './src/paradex.js';
import paymium from './src/paymium.js';
import phemex from './src/phemex.js';
import poloniex from './src/poloniex.js';
import probit from './src/probit.js';
import timex from './src/timex.js';
import tokocrypto from './src/tokocrypto.js';
import tradeogre from './src/tradeogre.js';
import upbit from './src/upbit.js';
import vertex from './src/vertex.js';
import wavesexchange from './src/wavesexchange.js';
import whitebit from './src/whitebit.js';
import woo from './src/woo.js';
import woofipro from './src/woofipro.js';
import xt from './src/xt.js';
import yobit from './src/yobit.js';
import zaif from './src/zaif.js';
import zonda from './src/zonda.js';
import alpacaPro from './src/pro/alpaca.js';
import apexPro from './src/pro/apex.js';
import ascendexPro from './src/pro/ascendex.js';
import bequantPro from './src/pro/bequant.js';
import binancePro from './src/pro/binance.js';
import binancecoinmPro from './src/pro/binancecoinm.js';
import binanceusPro from './src/pro/binanceus.js';
import binanceusdmPro from './src/pro/binanceusdm.js';
import bingxPro from './src/pro/bingx.js';
import bitfinexPro from './src/pro/bitfinex.js';
import bitgetPro from './src/pro/bitget.js';
import bithumbPro from './src/pro/bithumb.js';
import bitmartPro from './src/pro/bitmart.js';
import bitmexPro from './src/pro/bitmex.js';
import bitoproPro from './src/pro/bitopro.js';
import bitruePro from './src/pro/bitrue.js';
import bitstampPro from './src/pro/bitstamp.js';
import bittradePro from './src/pro/bittrade.js';
import bitvavoPro from './src/pro/bitvavo.js';
import blockchaincomPro from './src/pro/blockchaincom.js';
import blofinPro from './src/pro/blofin.js';
import bybitPro from './src/pro/bybit.js';
import cexPro from './src/pro/cex.js';
import coinbasePro from './src/pro/coinbase.js';
import coinbaseadvancedPro from './src/pro/coinbaseadvanced.js';
import coinbaseexchangePro from './src/pro/coinbaseexchange.js';
import coinbaseinternationalPro from './src/pro/coinbaseinternational.js';
import coincatchPro from './src/pro/coincatch.js';
import coincheckPro from './src/pro/coincheck.js';
import coinexPro from './src/pro/coinex.js';
import coinonePro from './src/pro/coinone.js';
import cryptocomPro from './src/pro/cryptocom.js';
import defxPro from './src/pro/defx.js';
import deribitPro from './src/pro/deribit.js';
import derivePro from './src/pro/derive.js';
import exmoPro from './src/pro/exmo.js';
import gatePro from './src/pro/gate.js';
import gateioPro from './src/pro/gateio.js';
import geminiPro from './src/pro/gemini.js';
import hashkeyPro from './src/pro/hashkey.js';
import hitbtcPro from './src/pro/hitbtc.js';
import hollaexPro from './src/pro/hollaex.js';
import htxPro from './src/pro/htx.js';
import huobiPro from './src/pro/huobi.js';
import hyperliquidPro from './src/pro/hyperliquid.js';
import independentreservePro from './src/pro/independentreserve.js';
import krakenPro from './src/pro/kraken.js';
import krakenfuturesPro from './src/pro/krakenfutures.js';
import kucoinPro from './src/pro/kucoin.js';
import kucoinfuturesPro from './src/pro/kucoinfutures.js';
import lbankPro from './src/pro/lbank.js';
import lunoPro from './src/pro/luno.js';
import mexcPro from './src/pro/mexc.js';
import modetradePro from './src/pro/modetrade.js';
import myokxPro from './src/pro/myokx.js';
import ndaxPro from './src/pro/ndax.js';
import okcoinPro from './src/pro/okcoin.js';
import okxPro from './src/pro/okx.js';
import okxusPro from './src/pro/okxus.js';
import onetradingPro from './src/pro/onetrading.js';
import oxfunPro from './src/pro/oxfun.js';
import p2bPro from './src/pro/p2b.js';
import paradexPro from './src/pro/paradex.js';
import phemexPro from './src/pro/phemex.js';
import poloniexPro from './src/pro/poloniex.js';
import probitPro from './src/pro/probit.js';
import tradeogrePro from './src/pro/tradeogre.js';
import upbitPro from './src/pro/upbit.js';
import vertexPro from './src/pro/vertex.js';
import whitebitPro from './src/pro/whitebit.js';
import wooPro from './src/pro/woo.js';
import woofiproPro from './src/pro/woofipro.js';
import xtPro from './src/pro/xt.js';
declare const exchanges: {
    alpaca: typeof alpaca;
    apex: typeof apex;
    ascendex: typeof ascendex;
    bequant: typeof bequant;
    bigone: typeof bigone;
    binance: typeof binance;
    binancecoinm: typeof binancecoinm;
    binanceus: typeof binanceus;
    binanceusdm: typeof binanceusdm;
    bingx: typeof bingx;
    bit2c: typeof bit2c;
    bitbank: typeof bitbank;
    bitbns: typeof bitbns;
    bitfinex: typeof bitfinex;
    bitflyer: typeof bitflyer;
    bitget: typeof bitget;
    bithumb: typeof bithumb;
    bitmart: typeof bitmart;
    bitmex: typeof bitmex;
    bitopro: typeof bitopro;
    bitrue: typeof bitrue;
    bitso: typeof bitso;
    bitstamp: typeof bitstamp;
    bitteam: typeof bitteam;
    bittrade: typeof bittrade;
    bitvavo: typeof bitvavo;
    blockchaincom: typeof blockchaincom;
    blofin: typeof blofin;
    btcalpha: typeof btcalpha;
    btcbox: typeof btcbox;
    btcmarkets: typeof btcmarkets;
    btcturk: typeof btcturk;
    bybit: typeof bybit;
    cex: typeof cex;
    coinbase: typeof coinbase;
    coinbaseadvanced: typeof coinbaseadvanced;
    coinbaseexchange: typeof coinbaseexchange;
    coinbaseinternational: typeof coinbaseinternational;
    coincatch: typeof coincatch;
    coincheck: typeof coincheck;
    coinex: typeof coinex;
    coinmate: typeof coinmate;
    coinmetro: typeof coinmetro;
    coinone: typeof coinone;
    coinsph: typeof coinsph;
    coinspot: typeof coinspot;
    cryptocom: typeof cryptocom;
    cryptomus: typeof cryptomus;
    defx: typeof defx;
    delta: typeof delta;
    deribit: typeof deribit;
    derive: typeof derive;
    digifinex: typeof digifinex;
    ellipx: typeof ellipx;
    exmo: typeof exmo;
    fmfwio: typeof fmfwio;
    gate: typeof gate;
    gateio: typeof gateio;
    gemini: typeof gemini;
    hashkey: typeof hashkey;
    hitbtc: typeof hitbtc;
    hollaex: typeof hollaex;
    htx: typeof htx;
    huobi: typeof huobi;
    hyperliquid: typeof hyperliquid;
    independentreserve: typeof independentreserve;
    indodax: typeof indodax;
    kraken: typeof kraken;
    krakenfutures: typeof krakenfutures;
    kucoin: typeof kucoin;
    kucoinfutures: typeof kucoinfutures;
    latoken: typeof latoken;
    lbank: typeof lbank;
    luno: typeof luno;
    mercado: typeof mercado;
    mexc: typeof mexc;
    modetrade: typeof modetrade;
    myokx: typeof myokx;
    ndax: typeof ndax;
    novadax: typeof novadax;
    oceanex: typeof oceanex;
    okcoin: typeof okcoin;
    okx: typeof okx;
    okxus: typeof okxus;
    onetrading: typeof onetrading;
    oxfun: typeof oxfun;
    p2b: typeof p2b;
    paradex: typeof paradex;
    paymium: typeof paymium;
    phemex: typeof phemex;
    poloniex: typeof poloniex;
    probit: typeof probit;
    timex: typeof timex;
    tokocrypto: typeof tokocrypto;
    tradeogre: typeof tradeogre;
    upbit: typeof upbit;
    vertex: typeof vertex;
    wavesexchange: typeof wavesexchange;
    whitebit: typeof whitebit;
    woo: typeof woo;
    woofipro: typeof woofipro;
    xt: typeof xt;
    yobit: typeof yobit;
    zaif: typeof zaif;
    zonda: typeof zonda;
};
declare const pro: {
    alpaca: typeof alpacaPro;
    apex: typeof apexPro;
    ascendex: typeof ascendexPro;
    bequant: typeof bequantPro;
    binance: typeof binancePro;
    binancecoinm: typeof binancecoinmPro;
    binanceus: typeof binanceusPro;
    binanceusdm: typeof binanceusdmPro;
    bingx: typeof bingxPro;
    bitfinex: typeof bitfinexPro;
    bitget: typeof bitgetPro;
    bithumb: typeof bithumbPro;
    bitmart: typeof bitmartPro;
    bitmex: typeof bitmexPro;
    bitopro: typeof bitoproPro;
    bitrue: typeof bitruePro;
    bitstamp: typeof bitstampPro;
    bittrade: typeof bittradePro;
    bitvavo: typeof bitvavoPro;
    blockchaincom: typeof blockchaincomPro;
    blofin: typeof blofinPro;
    bybit: typeof bybitPro;
    cex: typeof cexPro;
    coinbase: typeof coinbasePro;
    coinbaseadvanced: typeof coinbaseadvancedPro;
    coinbaseexchange: typeof coinbaseexchangePro;
    coinbaseinternational: typeof coinbaseinternationalPro;
    coincatch: typeof coincatchPro;
    coincheck: typeof coincheckPro;
    coinex: typeof coinexPro;
    coinone: typeof coinonePro;
    cryptocom: typeof cryptocomPro;
    defx: typeof defxPro;
    deribit: typeof deribitPro;
    derive: typeof derivePro;
    exmo: typeof exmoPro;
    gate: typeof gatePro;
    gateio: typeof gateioPro;
    gemini: typeof geminiPro;
    hashkey: typeof hashkeyPro;
    hitbtc: typeof hitbtcPro;
    hollaex: typeof hollaexPro;
    htx: typeof htxPro;
    huobi: typeof huobiPro;
    hyperliquid: typeof hyperliquidPro;
    independentreserve: typeof independentreservePro;
    kraken: typeof krakenPro;
    krakenfutures: typeof krakenfuturesPro;
    kucoin: typeof kucoinPro;
    kucoinfutures: typeof kucoinfuturesPro;
    lbank: typeof lbankPro;
    luno: typeof lunoPro;
    mexc: typeof mexcPro;
    modetrade: typeof modetradePro;
    myokx: typeof myokxPro;
    ndax: typeof ndaxPro;
    okcoin: typeof okcoinPro;
    okx: typeof okxPro;
    okxus: typeof okxusPro;
    onetrading: typeof onetradingPro;
    oxfun: typeof oxfunPro;
    p2b: typeof p2bPro;
    paradex: typeof paradexPro;
    phemex: typeof phemexPro;
    poloniex: typeof poloniexPro;
    probit: typeof probitPro;
    tradeogre: typeof tradeogrePro;
    upbit: typeof upbitPro;
    vertex: typeof vertexPro;
    whitebit: typeof whitebitPro;
    woo: typeof wooPro;
    woofipro: typeof woofiproPro;
    xt: typeof xtPro;
};
declare const ccxt: {
    version: string;
    Exchange: typeof Exchange;
    Precise: typeof Precise;
    exchanges: string[];
    pro: {
        alpaca: typeof alpacaPro;
        apex: typeof apexPro;
        ascendex: typeof ascendexPro;
        bequant: typeof bequantPro;
        binance: typeof binancePro;
        binancecoinm: typeof binancecoinmPro;
        binanceus: typeof binanceusPro;
        binanceusdm: typeof binanceusdmPro;
        bingx: typeof bingxPro;
        bitfinex: typeof bitfinexPro;
        bitget: typeof bitgetPro;
        bithumb: typeof bithumbPro;
        bitmart: typeof bitmartPro;
        bitmex: typeof bitmexPro;
        bitopro: typeof bitoproPro;
        bitrue: typeof bitruePro;
        bitstamp: typeof bitstampPro;
        bittrade: typeof bittradePro;
        bitvavo: typeof bitvavoPro;
        blockchaincom: typeof blockchaincomPro;
        blofin: typeof blofinPro;
        bybit: typeof bybitPro;
        cex: typeof cexPro;
        coinbase: typeof coinbasePro;
        coinbaseadvanced: typeof coinbaseadvancedPro;
        coinbaseexchange: typeof coinbaseexchangePro;
        coinbaseinternational: typeof coinbaseinternationalPro;
        coincatch: typeof coincatchPro;
        coincheck: typeof coincheckPro;
        coinex: typeof coinexPro;
        coinone: typeof coinonePro;
        cryptocom: typeof cryptocomPro;
        defx: typeof defxPro;
        deribit: typeof deribitPro;
        derive: typeof derivePro;
        exmo: typeof exmoPro;
        gate: typeof gatePro;
        gateio: typeof gateioPro;
        gemini: typeof geminiPro;
        hashkey: typeof hashkeyPro;
        hitbtc: typeof hitbtcPro;
        hollaex: typeof hollaexPro;
        htx: typeof htxPro;
        huobi: typeof huobiPro;
        hyperliquid: typeof hyperliquidPro;
        independentreserve: typeof independentreservePro;
        kraken: typeof krakenPro;
        krakenfutures: typeof krakenfuturesPro;
        kucoin: typeof kucoinPro;
        kucoinfutures: typeof kucoinfuturesPro;
        lbank: typeof lbankPro;
        luno: typeof lunoPro;
        mexc: typeof mexcPro;
        modetrade: typeof modetradePro;
        myokx: typeof myokxPro;
        ndax: typeof ndaxPro;
        okcoin: typeof okcoinPro;
        okx: typeof okxPro;
        okxus: typeof okxusPro;
        onetrading: typeof onetradingPro;
        oxfun: typeof oxfunPro;
        p2b: typeof p2bPro;
        paradex: typeof paradexPro;
        phemex: typeof phemexPro;
        poloniex: typeof poloniexPro;
        probit: typeof probitPro;
        tradeogre: typeof tradeogrePro;
        upbit: typeof upbitPro;
        vertex: typeof vertexPro;
        whitebit: typeof whitebitPro;
        woo: typeof wooPro;
        woofipro: typeof woofiproPro;
        xt: typeof xtPro;
    };
} & {
    alpaca: typeof alpaca;
    apex: typeof apex;
    ascendex: typeof ascendex;
    bequant: typeof bequant;
    bigone: typeof bigone;
    binance: typeof binance;
    binancecoinm: typeof binancecoinm;
    binanceus: typeof binanceus;
    binanceusdm: typeof binanceusdm;
    bingx: typeof bingx;
    bit2c: typeof bit2c;
    bitbank: typeof bitbank;
    bitbns: typeof bitbns;
    bitfinex: typeof bitfinex;
    bitflyer: typeof bitflyer;
    bitget: typeof bitget;
    bithumb: typeof bithumb;
    bitmart: typeof bitmart;
    bitmex: typeof bitmex;
    bitopro: typeof bitopro;
    bitrue: typeof bitrue;
    bitso: typeof bitso;
    bitstamp: typeof bitstamp;
    bitteam: typeof bitteam;
    bittrade: typeof bittrade;
    bitvavo: typeof bitvavo;
    blockchaincom: typeof blockchaincom;
    blofin: typeof blofin;
    btcalpha: typeof btcalpha;
    btcbox: typeof btcbox;
    btcmarkets: typeof btcmarkets;
    btcturk: typeof btcturk;
    bybit: typeof bybit;
    cex: typeof cex;
    coinbase: typeof coinbase;
    coinbaseadvanced: typeof coinbaseadvanced;
    coinbaseexchange: typeof coinbaseexchange;
    coinbaseinternational: typeof coinbaseinternational;
    coincatch: typeof coincatch;
    coincheck: typeof coincheck;
    coinex: typeof coinex;
    coinmate: typeof coinmate;
    coinmetro: typeof coinmetro;
    coinone: typeof coinone;
    coinsph: typeof coinsph;
    coinspot: typeof coinspot;
    cryptocom: typeof cryptocom;
    cryptomus: typeof cryptomus;
    defx: typeof defx;
    delta: typeof delta;
    deribit: typeof deribit;
    derive: typeof derive;
    digifinex: typeof digifinex;
    ellipx: typeof ellipx;
    exmo: typeof exmo;
    fmfwio: typeof fmfwio;
    gate: typeof gate;
    gateio: typeof gateio;
    gemini: typeof gemini;
    hashkey: typeof hashkey;
    hitbtc: typeof hitbtc;
    hollaex: typeof hollaex;
    htx: typeof htx;
    huobi: typeof huobi;
    hyperliquid: typeof hyperliquid;
    independentreserve: typeof independentreserve;
    indodax: typeof indodax;
    kraken: typeof kraken;
    krakenfutures: typeof krakenfutures;
    kucoin: typeof kucoin;
    kucoinfutures: typeof kucoinfutures;
    latoken: typeof latoken;
    lbank: typeof lbank;
    luno: typeof luno;
    mercado: typeof mercado;
    mexc: typeof mexc;
    modetrade: typeof modetrade;
    myokx: typeof myokx;
    ndax: typeof ndax;
    novadax: typeof novadax;
    oceanex: typeof oceanex;
    okcoin: typeof okcoin;
    okx: typeof okx;
    okxus: typeof okxus;
    onetrading: typeof onetrading;
    oxfun: typeof oxfun;
    p2b: typeof p2b;
    paradex: typeof paradex;
    paymium: typeof paymium;
    phemex: typeof phemex;
    poloniex: typeof poloniex;
    probit: typeof probit;
    timex: typeof timex;
    tokocrypto: typeof tokocrypto;
    tradeogre: typeof tradeogre;
    upbit: typeof upbit;
    vertex: typeof vertex;
    wavesexchange: typeof wavesexchange;
    whitebit: typeof whitebit;
    woo: typeof woo;
    woofipro: typeof woofipro;
    xt: typeof xt;
    yobit: typeof yobit;
    zaif: typeof zaif;
    zonda: typeof zonda;
} & typeof functions & typeof errors;
export { version, Exchange, exchanges, pro, Precise, functions, errors, BaseError, ExchangeError, AuthenticationError, PermissionDenied, AccountNotEnabled, AccountSuspended, ArgumentsRequired, BadRequest, BadSymbol, OperationRejected, NoChange, MarginModeAlreadySet, MarketClosed, ManualInteractionNeeded, InsufficientFunds, InvalidAddress, AddressPending, InvalidOrder, OrderNotFound, OrderNotCached, OrderImmediatelyFillable, OrderNotFillable, DuplicateOrderId, ContractUnavailable, NotSupported, InvalidProxySettings, ExchangeClosedByUser, OperationFailed, NetworkError, DDoSProtection, RateLimitExceeded, ExchangeNotAvailable, OnMaintenance, InvalidNonce, ChecksumError, RequestTimeout, BadResponse, NullResponse, CancelPending, UnsubscribeError, Int, int, Str, Strings, Num, Bool, IndexType, OrderSide, OrderType, MarketType, SubType, Dict, NullableDict, List, NullableList, Fee, OHLCV, OHLCVC, implicitReturnType, Market, Currency, ConstructorArgs, Dictionary, MinMax, FeeInterface, TradingFeeInterface, MarketMarginModes, MarketInterface, Trade, Order, OrderBook, OrderBooks, Ticker, Transaction, Tickers, CurrencyInterface, Balance, BalanceAccount, Account, PartialBalances, Balances, DepositAddress, WithdrawalResponse, FundingRate, FundingRates, Position, BorrowInterest, LeverageTier, LedgerEntry, DepositWithdrawFeeNetwork, DepositWithdrawFee, TransferEntry, CrossBorrowRate, IsolatedBorrowRate, FundingRateHistory, OpenInterest, OpenInterests, Liquidation, OrderRequest, CancellationRequest, FundingHistory, MarginMode, Greeks, Conversion, Option, LastPrice, Leverage, LongShortRatio, MarginModification, Leverages, LastPrices, Currencies, TradingFees, MarginModes, OptionChain, IsolatedBorrowRates, CrossBorrowRates, LeverageTiers, alpaca, apex, ascendex, bequant, bigone, binance, binancecoinm, binanceus, binanceusdm, bingx, bit2c, bitbank, bitbns, bitfinex, bitflyer, bitget, bithumb, bitmart, bitmex, bitopro, bitrue, bitso, bitstamp, bitteam, bittrade, bitvavo, blockchaincom, blofin, btcalpha, btcbox, btcmarkets, btcturk, bybit, cex, coinbase, coinbaseadvanced, coinbaseexchange, coinbaseinternational, coincatch, coincheck, coinex, coinmate, coinmetro, coinone, coinsph, coinspot, cryptocom, cryptomus, defx, delta, deribit, derive, digifinex, ellipx, exmo, fmfwio, gate, gateio, gemini, hashkey, hitbtc, hollaex, htx, huobi, hyperliquid, independentreserve, indodax, kraken, krakenfutures, kucoin, kucoinfutures, latoken, lbank, luno, mercado, mexc, modetrade, myokx, ndax, novadax, oceanex, okcoin, okx, okxus, onetrading, oxfun, p2b, paradex, paymium, phemex, poloniex, probit, timex, tokocrypto, tradeogre, upbit, vertex, wavesexchange, whitebit, woo, woofipro, xt, yobit, zaif, zonda, };
export default ccxt;
