# Server Configuration
PORT=3001
NODE_ENV=development

# Frontend URL for CORS
FRONTEND_URL=http://localhost:5173

# Exchange API Keys (Optional - for private endpoints)
# BINANCE_API_KEY=your_binance_api_key
# BINANCE_SECRET=your_binance_secret

# OKX_API_KEY=your_okx_api_key
# OKX_SECRET=your_okx_secret
# OKX_PASSPHRASE=your_okx_passphrase

# BYBIT_API_KEY=your_bybit_api_key
# BYBIT_SECRET=your_bybit_secret

# Database Configuration (Future use)
# DATABASE_URL=your_database_url

# Redis Configuration (Future use for caching)
# REDIS_URL=your_redis_url

# Logging Level
LOG_LEVEL=info
