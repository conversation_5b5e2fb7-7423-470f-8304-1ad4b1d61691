.exchange-card {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border: 1px solid #475569;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.exchange-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  border-color: #4ade80;
}

.exchange-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.exchange-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.exchange-icon {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2a2a2a;
  border-radius: 10px;
}

.exchange-details h3 {
  color: #ffffff;
  margin: 0 0 0.25rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.exchange-id {
  color: #9ca3af;
  margin: 0;
  font-size: 0.9rem;
  text-transform: uppercase;
  font-weight: 500;
}

.exchange-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-text {
  color: #9ca3af;
  font-size: 0.85rem;
}

.exchange-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-label {
  color: #9ca3af;
  font-size: 0.8rem;
  margin-bottom: 0.25rem;
}

.stat-value {
  color: #ffffff;
  font-weight: 600;
  font-size: 1.1rem;
}

.exchange-countries {
  margin-bottom: 1.5rem;
  padding: 0.75rem;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  border-left: 3px solid #4ade80;
}

.countries-label {
  color: #9ca3af;
  font-size: 0.85rem;
  font-weight: 500;
}

.countries-list {
  color: #ffffff;
  font-size: 0.9rem;
  margin-left: 0.5rem;
}

.exchange-actions {
  display: flex;
  gap: 0.75rem;
}

.action-button {
  flex: 1;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  text-decoration: none;
  text-align: center;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.2s;
  border: none;
  cursor: pointer;
}

.action-button.primary {
  background-color: #4ade80;
  color: #000000;
}

.action-button.primary:hover {
  background-color: #22c55e;
  transform: translateY(-1px);
}

.action-button.secondary {
  background-color: transparent;
  color: #9ca3af;
  border: 1px solid #475569;
}

.action-button.secondary:hover {
  background-color: #2a2a2a;
  color: #ffffff;
  border-color: #6b7280;
}

/* Responsive */
@media (max-width: 768px) {
  .exchange-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .exchange-status {
    align-self: flex-start;
  }
  
  .exchange-stats {
    grid-template-columns: 1fr;
  }
  
  .exchange-actions {
    flex-direction: column;
  }
}
