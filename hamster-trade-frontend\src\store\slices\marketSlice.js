import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { marketAPI } from '../../services/api'

// Async thunks
export const fetchExchanges = createAsyncThunk(
  'market/fetchExchanges',
  async () => {
    const response = await marketAPI.getExchanges()
    return response.data
  }
)

export const fetchMarkets = createAsyncThunk(
  'market/fetchMarkets',
  async (exchangeId) => {
    const response = await marketAPI.getMarkets(exchangeId)
    return { exchangeId, markets: response.data }
  }
)

export const searchMarkets = createAsyncThunk(
  'market/searchMarkets',
  async ({ query, exchange, type }) => {
    const response = await marketAPI.searchMarkets(query, exchange, type)
    return response.data
  }
)

export const fetchTicker = createAsyncThunk(
  'market/fetchTicker',
  async ({ exchangeId, symbol }) => {
    const response = await marketAPI.getTicker(exchangeId, symbol)
    return { symbol, ticker: response.data }
  }
)

const initialState = {
  exchanges: [],
  markets: {},
  searchResults: [],
  tickers: {},
  selectedExchange: null,
  selectedSymbol: null,
  loading: {
    exchanges: false,
    markets: false,
    search: false,
    ticker: false,
  },
  error: null,
}

const marketSlice = createSlice({
  name: 'market',
  initialState,
  reducers: {
    setSelectedExchange: (state, action) => {
      state.selectedExchange = action.payload
    },
    setSelectedSymbol: (state, action) => {
      state.selectedSymbol = action.payload
    },
    clearSearchResults: (state) => {
      state.searchResults = []
    },
    clearError: (state) => {
      state.error = null
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch exchanges
      .addCase(fetchExchanges.pending, (state) => {
        state.loading.exchanges = true
        state.error = null
      })
      .addCase(fetchExchanges.fulfilled, (state, action) => {
        state.loading.exchanges = false
        state.exchanges = action.payload
      })
      .addCase(fetchExchanges.rejected, (state, action) => {
        state.loading.exchanges = false
        state.error = action.error.message
      })
      // Fetch markets
      .addCase(fetchMarkets.pending, (state) => {
        state.loading.markets = true
        state.error = null
      })
      .addCase(fetchMarkets.fulfilled, (state, action) => {
        state.loading.markets = false
        state.markets[action.payload.exchangeId] = action.payload.markets
      })
      .addCase(fetchMarkets.rejected, (state, action) => {
        state.loading.markets = false
        state.error = action.error.message
      })
      // Search markets
      .addCase(searchMarkets.pending, (state) => {
        state.loading.search = true
        state.error = null
      })
      .addCase(searchMarkets.fulfilled, (state, action) => {
        state.loading.search = false
        state.searchResults = action.payload
      })
      .addCase(searchMarkets.rejected, (state, action) => {
        state.loading.search = false
        state.error = action.error.message
      })
      // Fetch ticker
      .addCase(fetchTicker.pending, (state) => {
        state.loading.ticker = true
      })
      .addCase(fetchTicker.fulfilled, (state, action) => {
        state.loading.ticker = false
        state.tickers[action.payload.symbol] = action.payload.ticker
      })
      .addCase(fetchTicker.rejected, (state, action) => {
        state.loading.ticker = false
        state.error = action.error.message
      })
  },
})

export const { 
  setSelectedExchange, 
  setSelectedSymbol, 
  clearSearchResults, 
  clearError 
} = marketSlice.actions

export default marketSlice.reducer
