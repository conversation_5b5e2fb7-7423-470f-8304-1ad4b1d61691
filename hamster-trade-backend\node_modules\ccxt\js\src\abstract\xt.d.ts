import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';
interface Exchange {
    publicSpotGetCurrencies(params?: {}): Promise<implicitReturnType>;
    publicSpotGetDepth(params?: {}): Promise<implicitReturnType>;
    publicSpotGetKline(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSymbol(params?: {}): Promise<implicitReturnType>;
    publicSpotGetTicker(params?: {}): Promise<implicitReturnType>;
    publicSpotGetTickerBook(params?: {}): Promise<implicitReturnType>;
    publicSpotGetTickerPrice(params?: {}): Promise<implicitReturnType>;
    publicSpotGetTicker24h(params?: {}): Promise<implicitReturnType>;
    publicSpotGetTime(params?: {}): Promise<implicitReturnType>;
    publicSpotGetTradeHistory(params?: {}): Promise<implicitReturnType>;
    publicSpotGetTradeRecent(params?: {}): Promise<implicitReturnType>;
    publicSpotGetWalletSupportCurrency(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicContractRiskBalance(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicContractOpenInterest(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicLeverageBracketDetail(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicLeverageBracketList(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicQAggTicker(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicQAggTickers(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicQDeal(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicQDepth(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicQFundingRate(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicQFundingRateRecord(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicQIndexPrice(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicQKline(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicQMarkPrice(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicQSymbolIndexPrice(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicQSymbolMarkPrice(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicQTicker(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicQTickers(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicSymbolCoins(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicSymbolDetail(params?: {}): Promise<implicitReturnType>;
    publicLinearGetFutureMarketV1PublicSymbolList(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicContractRiskBalance(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicContractOpenInterest(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicLeverageBracketDetail(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicLeverageBracketList(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicQAggTicker(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicQAggTickers(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicQDeal(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicQDepth(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicQFundingRate(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicQFundingRateRecord(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicQIndexPrice(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicQKline(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicQMarkPrice(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicQSymbolIndexPrice(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicQSymbolMarkPrice(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicQTicker(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicQTickers(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicSymbolCoins(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicSymbolDetail(params?: {}): Promise<implicitReturnType>;
    publicInverseGetFutureMarketV1PublicSymbolList(params?: {}): Promise<implicitReturnType>;
    privateSpotGetBalance(params?: {}): Promise<implicitReturnType>;
    privateSpotGetBalances(params?: {}): Promise<implicitReturnType>;
    privateSpotGetBatchOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotGetDepositAddress(params?: {}): Promise<implicitReturnType>;
    privateSpotGetDepositHistory(params?: {}): Promise<implicitReturnType>;
    privateSpotGetHistoryOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotGetOpenOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotGetOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotGetOrderOrderId(params?: {}): Promise<implicitReturnType>;
    privateSpotGetTrade(params?: {}): Promise<implicitReturnType>;
    privateSpotGetWithdrawHistory(params?: {}): Promise<implicitReturnType>;
    privateSpotPostOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotPostWithdraw(params?: {}): Promise<implicitReturnType>;
    privateSpotPostBalanceTransfer(params?: {}): Promise<implicitReturnType>;
    privateSpotPostBalanceAccountTransfer(params?: {}): Promise<implicitReturnType>;
    privateSpotPostWsToken(params?: {}): Promise<implicitReturnType>;
    privateSpotDeleteBatchOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotDeleteOpenOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotDeleteOrderOrderId(params?: {}): Promise<implicitReturnType>;
    privateSpotPutOrderOrderId(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureTradeV1EntrustPlanDetail(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureTradeV1EntrustPlanList(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureTradeV1EntrustPlanListHistory(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureTradeV1EntrustProfitDetail(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureTradeV1EntrustProfitList(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureTradeV1OrderDetail(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureTradeV1OrderList(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureTradeV1OrderListHistory(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureTradeV1OrderTradeList(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureUserV1AccountInfo(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureUserV1BalanceBills(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureUserV1BalanceDetail(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureUserV1BalanceFundingRateList(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureUserV1BalanceList(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureUserV1PositionAdl(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureUserV1PositionList(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureUserV1UserCollectionList(params?: {}): Promise<implicitReturnType>;
    privateLinearGetFutureUserV1UserListenKey(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureTradeV1EntrustCancelAllPlan(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureTradeV1EntrustCancelAllProfitStop(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureTradeV1EntrustCancelPlan(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureTradeV1EntrustCancelProfitStop(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureTradeV1EntrustCreatePlan(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureTradeV1EntrustCreateProfit(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureTradeV1EntrustUpdateProfitStop(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureTradeV1OrderCancel(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureTradeV1OrderCancelAll(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureTradeV1OrderCreate(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureTradeV1OrderCreateBatch(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureTradeV1OrderUpdate(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureUserV1AccountOpen(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureUserV1PositionAdjustLeverage(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureUserV1PositionAutoMargin(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureUserV1PositionCloseAll(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureUserV1PositionMargin(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureUserV1UserCollectionAdd(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureUserV1UserCollectionCancel(params?: {}): Promise<implicitReturnType>;
    privateLinearPostFutureUserV1PositionChangeType(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureTradeV1EntrustPlanDetail(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureTradeV1EntrustPlanList(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureTradeV1EntrustPlanListHistory(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureTradeV1EntrustProfitDetail(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureTradeV1EntrustProfitList(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureTradeV1OrderDetail(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureTradeV1OrderList(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureTradeV1OrderListHistory(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureTradeV1OrderTradeList(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureUserV1AccountInfo(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureUserV1BalanceBills(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureUserV1BalanceDetail(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureUserV1BalanceFundingRateList(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureUserV1BalanceList(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureUserV1PositionAdl(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureUserV1PositionList(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureUserV1UserCollectionList(params?: {}): Promise<implicitReturnType>;
    privateInverseGetFutureUserV1UserListenKey(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureTradeV1EntrustCancelAllPlan(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureTradeV1EntrustCancelAllProfitStop(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureTradeV1EntrustCancelPlan(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureTradeV1EntrustCancelProfitStop(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureTradeV1EntrustCreatePlan(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureTradeV1EntrustCreateProfit(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureTradeV1EntrustUpdateProfitStop(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureTradeV1OrderCancel(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureTradeV1OrderCancelAll(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureTradeV1OrderCreate(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureTradeV1OrderCreateBatch(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureTradeV1OrderUpdate(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureUserV1AccountOpen(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureUserV1PositionAdjustLeverage(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureUserV1PositionAutoMargin(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureUserV1PositionCloseAll(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureUserV1PositionMargin(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureUserV1UserCollectionAdd(params?: {}): Promise<implicitReturnType>;
    privateInversePostFutureUserV1UserCollectionCancel(params?: {}): Promise<implicitReturnType>;
    privateUserGetUserAccount(params?: {}): Promise<implicitReturnType>;
    privateUserGetUserAccountApiKey(params?: {}): Promise<implicitReturnType>;
    privateUserPostUserAccount(params?: {}): Promise<implicitReturnType>;
    privateUserPostUserAccountApiKey(params?: {}): Promise<implicitReturnType>;
    privateUserPutUserAccountApiKey(params?: {}): Promise<implicitReturnType>;
    privateUserDeleteUserAccountApikeyId(params?: {}): Promise<implicitReturnType>;
}
declare abstract class Exchange extends _Exchange {
}
export default Exchange;
