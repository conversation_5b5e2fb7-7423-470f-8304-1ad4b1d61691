import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { marketAPI } from '../../services/api'

// Async thunks
export const fetchOHLCV = createAsyncThunk(
  'chart/fetchOHLCV',
  async ({ exchangeId, symbol, timeframe, limit }) => {
    const response = await marketAPI.getOHLCV(exchangeId, symbol, timeframe, limit)
    return {
      exchangeId,
      symbol,
      timeframe,
      data: response.data
    }
  }
)

const initialState = {
  ohlcvData: {},
  currentSymbol: null,
  currentExchange: null,
  currentTimeframe: '1m',
  availableTimeframes: ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w'],
  chartInstance: null,
  indicators: [],
  loading: false,
  error: null,
}

const chartSlice = createSlice({
  name: 'chart',
  initialState,
  reducers: {
    setCurrentSymbol: (state, action) => {
      state.currentSymbol = action.payload
    },
    setCurrentExchange: (state, action) => {
      state.currentExchange = action.payload
    },
    setCurrentTimeframe: (state, action) => {
      state.currentTimeframe = action.payload
    },
    setChartInstance: (state, action) => {
      state.chartInstance = action.payload
    },
    addIndicator: (state, action) => {
      state.indicators.push(action.payload)
    },
    removeIndicator: (state, action) => {
      state.indicators = state.indicators.filter(
        indicator => indicator.id !== action.payload
      )
    },
    updateIndicator: (state, action) => {
      const { id, updates } = action.payload
      const indicator = state.indicators.find(ind => ind.id === id)
      if (indicator) {
        Object.assign(indicator, updates)
      }
    },
    updateOHLCVData: (state, action) => {
      const { exchangeId, symbol, timeframe, data } = action.payload
      const key = `${exchangeId}:${symbol}:${timeframe}`
      state.ohlcvData[key] = data
    },
    clearError: (state) => {
      state.error = null
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchOHLCV.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchOHLCV.fulfilled, (state, action) => {
        state.loading = false
        const { exchangeId, symbol, timeframe, data } = action.payload
        const key = `${exchangeId}:${symbol}:${timeframe}`
        state.ohlcvData[key] = data
      })
      .addCase(fetchOHLCV.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message
      })
  },
})

export const {
  setCurrentSymbol,
  setCurrentExchange,
  setCurrentTimeframe,
  setChartInstance,
  addIndicator,
  removeIndicator,
  updateIndicator,
  updateOHLCVData,
  clearError,
} = chartSlice.actions

export default chartSlice.reducer
