import { configureStore } from '@reduxjs/toolkit'
import marketReducer from './slices/marketSlice'
import chartReducer from './slices/chartSlice'
import exchangeReducer from './slices/exchangeSlice'

export const store = configureStore({
  reducer: {
    market: marketReducer,
    chart: chartReducer,
    exchange: exchangeReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['chart/updateChart'],
        ignoredPaths: ['chart.chartInstance'],
      },
    }),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
