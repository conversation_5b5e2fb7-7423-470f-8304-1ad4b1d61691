import { useEffect, useState } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { Link } from 'react-router-dom'
import { fetchExchanges, searchMarkets } from '../../store/slices/marketSlice'
import SearchBar from '../../components/SearchBar/SearchBar'
import ExchangeCard from '../../components/ExchangeCard/ExchangeCard'
import MarketList from '../../components/MarketList/MarketList'
import './Dashboard.css'

const Dashboard = () => {
  const dispatch = useDispatch()
  const { exchanges, searchResults, loading } = useSelector(state => state.market)
  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    if (exchanges.length === 0) {
      dispatch(fetchExchanges())
    }
  }, [dispatch, exchanges.length])

  const handleSearch = (query) => {
    setSearchQuery(query)
    if (query.trim()) {
      dispatch(searchMarkets({ query: query.trim() }))
    }
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>Dashboard</h1>
        <p>Welcome to Hamster Trade - Your Crypto Trading Analysis Platform</p>
      </div>

      {/* Search Section */}
      <section className="search-section">
        <SearchBar 
          onSearch={handleSearch}
          placeholder="Search cryptocurrencies..."
        />
        
        {searchQuery && (
          <div className="search-results">
            <h3>Search Results for "{searchQuery}"</h3>
            {loading.search ? (
              <div className="loading">Searching...</div>
            ) : (
              <MarketList markets={searchResults} />
            )}
          </div>
        )}
      </section>

      {/* Quick Actions */}
      <section className="quick-actions">
        <h2>Quick Actions</h2>
        <div className="action-cards">
          <Link to="/chart" className="action-card">
            <div className="action-icon">📈</div>
            <h3>Open Chart</h3>
            <p>Start analyzing with advanced charting tools</p>
          </Link>
          
          <Link to="/markets" className="action-card">
            <div className="action-icon">🏪</div>
            <h3>Browse Markets</h3>
            <p>Explore all available trading pairs</p>
          </Link>
          
          <div className="action-card">
            <div className="action-icon">⚙️</div>
            <h3>Indicators</h3>
            <p>Manage your technical indicators</p>
          </div>
        </div>
      </section>

      {/* Exchanges Overview */}
      <section className="exchanges-section">
        <h2>Connected Exchanges</h2>
        {loading.exchanges ? (
          <div className="loading">Loading exchanges...</div>
        ) : (
          <div className="exchanges-grid">
            {exchanges.map((exchange) => (
              <ExchangeCard 
                key={exchange.id} 
                exchange={exchange}
              />
            ))}
          </div>
        )}
      </section>

      {/* Popular Markets */}
      <section className="popular-markets">
        <h2>Popular Markets</h2>
        <div className="market-categories">
          <div className="category-tabs">
            <button className="tab active">Spot</button>
            <button className="tab">Futures</button>
            <button className="tab">Top Gainers</button>
            <button className="tab">Top Volume</button>
          </div>
          
          <div className="popular-list">
            <div className="market-item">
              <span className="symbol">BTC/USDT</span>
              <span className="price">$43,250.00</span>
              <span className="change positive">+2.45%</span>
            </div>
            <div className="market-item">
              <span className="symbol">ETH/USDT</span>
              <span className="price">$2,580.50</span>
              <span className="change positive">+1.23%</span>
            </div>
            <div className="market-item">
              <span className="symbol">BNB/USDT</span>
              <span className="price">$315.80</span>
              <span className="change negative">-0.85%</span>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Dashboard
