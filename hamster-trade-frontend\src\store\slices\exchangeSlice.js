import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  connectedExchanges: [],
  connectionStatus: {},
  subscriptions: {},
  realTimeData: {},
}

const exchangeSlice = createSlice({
  name: 'exchange',
  initialState,
  reducers: {
    addConnectedExchange: (state, action) => {
      const exchangeId = action.payload
      if (!state.connectedExchanges.includes(exchangeId)) {
        state.connectedExchanges.push(exchangeId)
        state.connectionStatus[exchangeId] = 'connected'
      }
    },
    removeConnectedExchange: (state, action) => {
      const exchangeId = action.payload
      state.connectedExchanges = state.connectedExchanges.filter(
        id => id !== exchangeId
      )
      delete state.connectionStatus[exchangeId]
      delete state.subscriptions[exchangeId]
      delete state.realTimeData[exchangeId]
    },
    setConnectionStatus: (state, action) => {
      const { exchangeId, status } = action.payload
      state.connectionStatus[exchangeId] = status
    },
    addSubscription: (state, action) => {
      const { exchangeId, symbol, timeframe } = action.payload
      if (!state.subscriptions[exchangeId]) {
        state.subscriptions[exchangeId] = {}
      }
      if (!state.subscriptions[exchangeId][symbol]) {
        state.subscriptions[exchangeId][symbol] = []
      }
      if (!state.subscriptions[exchangeId][symbol].includes(timeframe)) {
        state.subscriptions[exchangeId][symbol].push(timeframe)
      }
    },
    removeSubscription: (state, action) => {
      const { exchangeId, symbol, timeframe } = action.payload
      if (state.subscriptions[exchangeId]?.[symbol]) {
        state.subscriptions[exchangeId][symbol] = state.subscriptions[exchangeId][symbol].filter(
          tf => tf !== timeframe
        )
        if (state.subscriptions[exchangeId][symbol].length === 0) {
          delete state.subscriptions[exchangeId][symbol]
        }
      }
    },
    updateRealTimeData: (state, action) => {
      const { exchangeId, symbol, timeframe, data } = action.payload
      if (!state.realTimeData[exchangeId]) {
        state.realTimeData[exchangeId] = {}
      }
      if (!state.realTimeData[exchangeId][symbol]) {
        state.realTimeData[exchangeId][symbol] = {}
      }
      state.realTimeData[exchangeId][symbol][timeframe] = data
    },
    clearRealTimeData: (state, action) => {
      const { exchangeId, symbol } = action.payload
      if (state.realTimeData[exchangeId]?.[symbol]) {
        delete state.realTimeData[exchangeId][symbol]
      }
    },
  },
})

export const {
  addConnectedExchange,
  removeConnectedExchange,
  setConnectionStatus,
  addSubscription,
  removeSubscription,
  updateRealTimeData,
  clearRealTimeData,
} = exchangeSlice.actions

export default exchangeSlice.reducer
