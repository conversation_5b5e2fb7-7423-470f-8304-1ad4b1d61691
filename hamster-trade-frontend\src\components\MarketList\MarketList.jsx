import { Link } from 'react-router-dom'
import './MarketList.css'

const MarketList = ({ markets = [], showExchange = true, limit = null }) => {
  const displayMarkets = limit ? markets.slice(0, limit) : markets

  const getMarketTypeIcon = (type) => {
    const icons = {
      spot: '💰',
      future: '📈',
      swap: '🔄',
      option: '📊',
    }
    return icons[type] || '💱'
  }

  const getMarketTypeBadge = (market) => {
    if (market.spot) return { text: 'Spot', class: 'spot' }
    if (market.future) return { text: 'Future', class: 'future' }
    return { text: 'Other', class: 'other' }
  }

  if (!markets || markets.length === 0) {
    return (
      <div className="market-list-empty">
        <div className="empty-icon">📊</div>
        <p>No markets found</p>
      </div>
    )
  }

  return (
    <div className="market-list">
      <div className="market-list-header">
        <div className="header-cell symbol">Symbol</div>
        <div className="header-cell type">Type</div>
        {showExchange && <div className="header-cell exchange">Exchange</div>}
        <div className="header-cell status">Status</div>
        <div className="header-cell actions">Actions</div>
      </div>

      <div className="market-list-body">
        {displayMarkets.map((market, index) => {
          const typeBadge = getMarketTypeBadge(market)
          
          return (
            <div key={`${market.exchange}-${market.symbol}-${index}`} className="market-row">
              <div className="market-cell symbol">
                <div className="symbol-info">
                  <div className="symbol-icon">
                    {getMarketTypeIcon(market.type)}
                  </div>
                  <div className="symbol-details">
                    <span className="symbol-name">{market.symbol}</span>
                    <span className="symbol-pair">
                      {market.base}/{market.quote}
                    </span>
                  </div>
                </div>
              </div>

              <div className="market-cell type">
                <span className={`type-badge ${typeBadge.class}`}>
                  {typeBadge.text}
                </span>
              </div>

              {showExchange && (
                <div className="market-cell exchange">
                  <div className="exchange-info">
                    <span className="exchange-name">{market.exchangeName}</span>
                    <span className="exchange-id">{market.exchange}</span>
                  </div>
                </div>
              )}

              <div className="market-cell status">
                <span className={`status-badge ${market.active ? 'active' : 'inactive'}`}>
                  {market.active ? 'Active' : 'Inactive'}
                </span>
              </div>

              <div className="market-cell actions">
                <Link
                  to={`/chart/${market.exchange}/${encodeURIComponent(market.symbol)}`}
                  className="action-btn chart"
                  title="Open Chart"
                >
                  📈
                </Link>
                <button
                  className="action-btn info"
                  title="Market Info"
                  onClick={() => console.log('Market info:', market)}
                >
                  ℹ️
                </button>
              </div>
            </div>
          )
        })}
      </div>

      {limit && markets.length > limit && (
        <div className="market-list-footer">
          <p className="showing-count">
            Showing {limit} of {markets.length} markets
          </p>
        </div>
      )}
    </div>
  )
}

export default MarketList
