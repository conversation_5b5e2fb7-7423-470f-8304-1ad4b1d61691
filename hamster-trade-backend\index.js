import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import dotenv from 'dotenv';
import ccxt from 'ccxt';

// Load environment variables
dotenv.config();

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:5173",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Store active exchanges
const exchanges = {};

// Initialize some popular exchanges
const initializeExchanges = () => {
  try {
    exchanges.binance = new ccxt.binance({
      sandbox: false,
      enableRateLimit: true,
    });
    
    exchanges.okx = new ccxt.okx({
      sandbox: false,
      enableRateLimit: true,
    });
    
    exchanges.bybit = new ccxt.bybit({
      sandbox: false,
      enableRateLimit: true,
    });
    
    console.log('Exchanges initialized successfully');
  } catch (error) {
    console.error('Error initializing exchanges:', error);
  }
};

// API Routes

// Get available exchanges
app.get('/api/exchanges', (req, res) => {
  const exchangeList = Object.keys(exchanges).map(id => ({
    id,
    name: exchanges[id].name,
    countries: exchanges[id].countries,
    urls: exchanges[id].urls
  }));
  res.json(exchangeList);
});

// Get markets for a specific exchange
app.get('/api/exchanges/:exchangeId/markets', async (req, res) => {
  try {
    const { exchangeId } = req.params;
    const exchange = exchanges[exchangeId];
    
    if (!exchange) {
      return res.status(404).json({ error: 'Exchange not found' });
    }
    
    const markets = await exchange.loadMarkets();
    const marketList = Object.values(markets).map(market => ({
      symbol: market.symbol,
      base: market.base,
      quote: market.quote,
      type: market.type,
      spot: market.spot,
      future: market.future,
      active: market.active
    }));
    
    res.json(marketList);
  } catch (error) {
    console.error('Error fetching markets:', error);
    res.status(500).json({ error: 'Failed to fetch markets' });
  }
});

// Get ticker data
app.get('/api/exchanges/:exchangeId/ticker/:symbol', async (req, res) => {
  try {
    const { exchangeId, symbol } = req.params;
    const exchange = exchanges[exchangeId];
    
    if (!exchange) {
      return res.status(404).json({ error: 'Exchange not found' });
    }
    
    const ticker = await exchange.fetchTicker(symbol);
    res.json(ticker);
  } catch (error) {
    console.error('Error fetching ticker:', error);
    res.status(500).json({ error: 'Failed to fetch ticker data' });
  }
});

// Get OHLCV data
app.get('/api/exchanges/:exchangeId/ohlcv/:symbol', async (req, res) => {
  try {
    const { exchangeId, symbol } = req.params;
    const { timeframe = '1m', limit = 100 } = req.query;
    const exchange = exchanges[exchangeId];
    
    if (!exchange) {
      return res.status(404).json({ error: 'Exchange not found' });
    }
    
    const ohlcv = await exchange.fetchOHLCV(symbol, timeframe, undefined, parseInt(limit));
    res.json(ohlcv);
  } catch (error) {
    console.error('Error fetching OHLCV:', error);
    res.status(500).json({ error: 'Failed to fetch OHLCV data' });
  }
});

// Search markets
app.get('/api/search', async (req, res) => {
  try {
    const { query, exchange: exchangeId, type } = req.query;
    
    if (!query) {
      return res.status(400).json({ error: 'Query parameter is required' });
    }
    
    const results = [];
    const exchangesToSearch = exchangeId ? [exchangeId] : Object.keys(exchanges);
    
    for (const exId of exchangesToSearch) {
      const exchange = exchanges[exId];
      if (!exchange) continue;
      
      try {
        const markets = await exchange.loadMarkets();
        const filteredMarkets = Object.values(markets)
          .filter(market => {
            const matchesQuery = market.symbol.toLowerCase().includes(query.toLowerCase()) ||
                               market.base.toLowerCase().includes(query.toLowerCase()) ||
                               market.quote.toLowerCase().includes(query.toLowerCase());
            
            const matchesType = !type || 
                               (type === 'spot' && market.spot) ||
                               (type === 'future' && market.future);
            
            return matchesQuery && matchesType && market.active;
          })
          .slice(0, 20) // Limit results per exchange
          .map(market => ({
            symbol: market.symbol,
            base: market.base,
            quote: market.quote,
            type: market.type,
            exchange: exId,
            exchangeName: exchange.name
          }));
        
        results.push(...filteredMarkets);
      } catch (error) {
        console.error(`Error searching in ${exId}:`, error);
      }
    }
    
    res.json(results.slice(0, 50)); // Limit total results
  } catch (error) {
    console.error('Error in search:', error);
    res.status(500).json({ error: 'Search failed' });
  }
});

// WebSocket connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  socket.on('subscribe', (data) => {
    const { exchange, symbol, timeframe } = data;
    console.log(`Client ${socket.id} subscribed to ${exchange}:${symbol}:${timeframe}`);
    
    // Join room for this specific subscription
    const room = `${exchange}:${symbol}:${timeframe}`;
    socket.join(room);
  });
  
  socket.on('unsubscribe', (data) => {
    const { exchange, symbol, timeframe } = data;
    const room = `${exchange}:${symbol}:${timeframe}`;
    socket.leave(room);
    console.log(`Client ${socket.id} unsubscribed from ${room}`);
  });
  
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

// Initialize exchanges and start server
initializeExchanges();

server.listen(PORT, () => {
  console.log(`Hamster Trade Backend running on port ${PORT}`);
});

export default app;
