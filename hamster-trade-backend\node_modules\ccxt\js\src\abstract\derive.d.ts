import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';
interface Exchange {
    publicGetGetAllCurrencies(params?: {}): Promise<implicitReturnType>;
    publicPostBuildRegisterSessionKeyTx(params?: {}): Promise<implicitReturnType>;
    publicPostRegisterSessionKey(params?: {}): Promise<implicitReturnType>;
    publicPostDeregisterSessionKey(params?: {}): Promise<implicitReturnType>;
    publicPostLogin(params?: {}): Promise<implicitReturnType>;
    publicPostStatistics(params?: {}): Promise<implicitReturnType>;
    publicPostGetAllCurrencies(params?: {}): Promise<implicitReturnType>;
    publicPostGetCurrency(params?: {}): Promise<implicitReturnType>;
    publicPostGetInstrument(params?: {}): Promise<implicitReturnType>;
    publicPostGetAllInstruments(params?: {}): Promise<implicitReturnType>;
    publicPostGetInstruments(params?: {}): Promise<implicitReturnType>;
    publicPostGetTicker(params?: {}): Promise<implicitReturnType>;
    publicPostGetLatestSignedFeeds(params?: {}): Promise<implicitReturnType>;
    publicPostGetOptionSettlementPrices(params?: {}): Promise<implicitReturnType>;
    publicPostGetSpotFeedHistory(params?: {}): Promise<implicitReturnType>;
    publicPostGetSpotFeedHistoryCandles(params?: {}): Promise<implicitReturnType>;
    publicPostGetFundingRateHistory(params?: {}): Promise<implicitReturnType>;
    publicPostGetTradeHistory(params?: {}): Promise<implicitReturnType>;
    publicPostGetOptionSettlementHistory(params?: {}): Promise<implicitReturnType>;
    publicPostGetLiquidationHistory(params?: {}): Promise<implicitReturnType>;
    publicPostGetInterestRateHistory(params?: {}): Promise<implicitReturnType>;
    publicPostGetTransaction(params?: {}): Promise<implicitReturnType>;
    publicPostGetMargin(params?: {}): Promise<implicitReturnType>;
    publicPostMarginWatch(params?: {}): Promise<implicitReturnType>;
    publicPostValidateInviteCode(params?: {}): Promise<implicitReturnType>;
    publicPostGetPoints(params?: {}): Promise<implicitReturnType>;
    publicPostGetAllPoints(params?: {}): Promise<implicitReturnType>;
    publicPostGetPointsLeaderboard(params?: {}): Promise<implicitReturnType>;
    publicPostGetDescendantTree(params?: {}): Promise<implicitReturnType>;
    publicPostGetTreeRoots(params?: {}): Promise<implicitReturnType>;
    publicPostGetSwellPercentPoints(params?: {}): Promise<implicitReturnType>;
    publicPostGetVaultAssets(params?: {}): Promise<implicitReturnType>;
    publicPostGetEtherfiEffectiveBalances(params?: {}): Promise<implicitReturnType>;
    publicPostGetKelpEffectiveBalances(params?: {}): Promise<implicitReturnType>;
    publicPostGetBridgeBalances(params?: {}): Promise<implicitReturnType>;
    publicPostGetEthenaParticipants(params?: {}): Promise<implicitReturnType>;
    publicPostGetVaultShare(params?: {}): Promise<implicitReturnType>;
    publicPostGetVaultStatistics(params?: {}): Promise<implicitReturnType>;
    publicPostGetVaultBalances(params?: {}): Promise<implicitReturnType>;
    publicPostEstimateIntegratorPoints(params?: {}): Promise<implicitReturnType>;
    publicPostCreateSubaccountDebug(params?: {}): Promise<implicitReturnType>;
    publicPostDepositDebug(params?: {}): Promise<implicitReturnType>;
    publicPostWithdrawDebug(params?: {}): Promise<implicitReturnType>;
    publicPostSendQuoteDebug(params?: {}): Promise<implicitReturnType>;
    publicPostExecuteQuoteDebug(params?: {}): Promise<implicitReturnType>;
    publicPostGetInviteCode(params?: {}): Promise<implicitReturnType>;
    publicPostRegisterInvite(params?: {}): Promise<implicitReturnType>;
    publicPostGetTime(params?: {}): Promise<implicitReturnType>;
    publicPostGetLiveIncidents(params?: {}): Promise<implicitReturnType>;
    publicPostGetMakerPrograms(params?: {}): Promise<implicitReturnType>;
    publicPostGetMakerProgramScores(params?: {}): Promise<implicitReturnType>;
    privatePostGetAccount(params?: {}): Promise<implicitReturnType>;
    privatePostCreateSubaccount(params?: {}): Promise<implicitReturnType>;
    privatePostGetSubaccount(params?: {}): Promise<implicitReturnType>;
    privatePostGetSubaccounts(params?: {}): Promise<implicitReturnType>;
    privatePostGetAllPortfolios(params?: {}): Promise<implicitReturnType>;
    privatePostChangeSubaccountLabel(params?: {}): Promise<implicitReturnType>;
    privatePostGetNotificationsv(params?: {}): Promise<implicitReturnType>;
    privatePostUpdateNotifications(params?: {}): Promise<implicitReturnType>;
    privatePostDeposit(params?: {}): Promise<implicitReturnType>;
    privatePostWithdraw(params?: {}): Promise<implicitReturnType>;
    privatePostTransferErc20(params?: {}): Promise<implicitReturnType>;
    privatePostTransferPosition(params?: {}): Promise<implicitReturnType>;
    privatePostTransferPositions(params?: {}): Promise<implicitReturnType>;
    privatePostOrder(params?: {}): Promise<implicitReturnType>;
    privatePostReplace(params?: {}): Promise<implicitReturnType>;
    privatePostOrderDebug(params?: {}): Promise<implicitReturnType>;
    privatePostGetOrder(params?: {}): Promise<implicitReturnType>;
    privatePostGetOrders(params?: {}): Promise<implicitReturnType>;
    privatePostGetOpenOrders(params?: {}): Promise<implicitReturnType>;
    privatePostCancel(params?: {}): Promise<implicitReturnType>;
    privatePostCancelByLabel(params?: {}): Promise<implicitReturnType>;
    privatePostCancelByNonce(params?: {}): Promise<implicitReturnType>;
    privatePostCancelByInstrument(params?: {}): Promise<implicitReturnType>;
    privatePostCancelAll(params?: {}): Promise<implicitReturnType>;
    privatePostCancelTriggerOrder(params?: {}): Promise<implicitReturnType>;
    privatePostGetOrderHistory(params?: {}): Promise<implicitReturnType>;
    privatePostGetTradeHistory(params?: {}): Promise<implicitReturnType>;
    privatePostGetDepositHistory(params?: {}): Promise<implicitReturnType>;
    privatePostGetWithdrawalHistory(params?: {}): Promise<implicitReturnType>;
    privatePostSendRfq(params?: {}): Promise<implicitReturnType>;
    privatePostCancelRfq(params?: {}): Promise<implicitReturnType>;
    privatePostCancelBatchRfqs(params?: {}): Promise<implicitReturnType>;
    privatePostGetRfqs(params?: {}): Promise<implicitReturnType>;
    privatePostPollRfqs(params?: {}): Promise<implicitReturnType>;
    privatePostSendQuote(params?: {}): Promise<implicitReturnType>;
    privatePostCancelQuote(params?: {}): Promise<implicitReturnType>;
    privatePostCancelBatchQuotes(params?: {}): Promise<implicitReturnType>;
    privatePostGetQuotes(params?: {}): Promise<implicitReturnType>;
    privatePostPollQuotes(params?: {}): Promise<implicitReturnType>;
    privatePostExecuteQuote(params?: {}): Promise<implicitReturnType>;
    privatePostRfqGetBestQuote(params?: {}): Promise<implicitReturnType>;
    privatePostGetMargin(params?: {}): Promise<implicitReturnType>;
    privatePostGetCollaterals(params?: {}): Promise<implicitReturnType>;
    privatePostGetPositions(params?: {}): Promise<implicitReturnType>;
    privatePostGetOptionSettlementHistory(params?: {}): Promise<implicitReturnType>;
    privatePostGetSubaccountValueHistory(params?: {}): Promise<implicitReturnType>;
    privatePostExpiredAndCancelledHistory(params?: {}): Promise<implicitReturnType>;
    privatePostGetFundingHistory(params?: {}): Promise<implicitReturnType>;
    privatePostGetInterestHistory(params?: {}): Promise<implicitReturnType>;
    privatePostGetErc20TransferHistory(params?: {}): Promise<implicitReturnType>;
    privatePostGetLiquidationHistory(params?: {}): Promise<implicitReturnType>;
    privatePostLiquidate(params?: {}): Promise<implicitReturnType>;
    privatePostGetLiquidatorHistory(params?: {}): Promise<implicitReturnType>;
    privatePostSessionKeys(params?: {}): Promise<implicitReturnType>;
    privatePostEditSessionKey(params?: {}): Promise<implicitReturnType>;
    privatePostRegisterScopedSessionKey(params?: {}): Promise<implicitReturnType>;
    privatePostGetMmpConfig(params?: {}): Promise<implicitReturnType>;
    privatePostSetMmpConfig(params?: {}): Promise<implicitReturnType>;
    privatePostResetMmp(params?: {}): Promise<implicitReturnType>;
    privatePostSetCancelOnDisconnect(params?: {}): Promise<implicitReturnType>;
    privatePostGetInviteCode(params?: {}): Promise<implicitReturnType>;
    privatePostRegisterInvite(params?: {}): Promise<implicitReturnType>;
}
declare abstract class Exchange extends _Exchange {
}
export default Exchange;
