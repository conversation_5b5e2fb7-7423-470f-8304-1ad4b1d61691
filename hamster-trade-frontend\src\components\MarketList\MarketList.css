.market-list {
  background-color: #1a1a1a;
  border-radius: 12px;
  border: 1px solid #333;
  overflow: hidden;
}

.market-list-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr 1fr 1fr;
  background-color: #2a2a2a;
  border-bottom: 1px solid #333;
  padding: 1rem;
  font-weight: 600;
  color: #9ca3af;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.market-list-header.no-exchange {
  grid-template-columns: 2fr 1fr 1fr 1fr;
}

.header-cell {
  display: flex;
  align-items: center;
}

.market-list-body {
  max-height: 400px;
  overflow-y: auto;
}

.market-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr 1fr 1fr;
  padding: 1rem;
  border-bottom: 1px solid #333;
  transition: background-color 0.2s;
}

.market-row.no-exchange {
  grid-template-columns: 2fr 1fr 1fr 1fr;
}

.market-row:hover {
  background-color: #2a2a2a;
}

.market-row:last-child {
  border-bottom: none;
}

.market-cell {
  display: flex;
  align-items: center;
}

.symbol-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.symbol-icon {
  font-size: 1.2rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #333;
  border-radius: 6px;
}

.symbol-details {
  display: flex;
  flex-direction: column;
}

.symbol-name {
  color: #ffffff;
  font-weight: 600;
  font-size: 1rem;
}

.symbol-pair {
  color: #9ca3af;
  font-size: 0.85rem;
}

.type-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.type-badge.spot {
  background-color: rgba(74, 222, 128, 0.2);
  color: #4ade80;
  border: 1px solid rgba(74, 222, 128, 0.3);
}

.type-badge.future {
  background-color: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.type-badge.other {
  background-color: rgba(156, 163, 175, 0.2);
  color: #9ca3af;
  border: 1px solid rgba(156, 163, 175, 0.3);
}

.exchange-info {
  display: flex;
  flex-direction: column;
}

.exchange-name {
  color: #ffffff;
  font-weight: 500;
  font-size: 0.9rem;
}

.exchange-id {
  color: #9ca3af;
  font-size: 0.8rem;
  text-transform: uppercase;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.active {
  background-color: rgba(74, 222, 128, 0.2);
  color: #4ade80;
  border: 1px solid rgba(74, 222, 128, 0.3);
}

.status-badge.inactive {
  background-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #333;
  border: 1px solid #475569;
  border-radius: 6px;
  color: #9ca3af;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.action-btn:hover {
  background-color: #4ade80;
  color: #000000;
  border-color: #4ade80;
  transform: translateY(-1px);
}

.market-list-footer {
  padding: 1rem;
  text-align: center;
  border-top: 1px solid #333;
  background-color: #2a2a2a;
}

.showing-count {
  color: #9ca3af;
  font-size: 0.9rem;
  margin: 0;
}

.market-list-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #9ca3af;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.market-list-empty p {
  margin: 0;
  font-size: 1.1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .market-list-header,
  .market-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .market-cell {
    justify-content: space-between;
    padding: 0.25rem 0;
  }
  
  .market-cell::before {
    content: attr(data-label);
    font-weight: 600;
    color: #9ca3af;
    font-size: 0.8rem;
    text-transform: uppercase;
  }
  
  .header-cell {
    display: none;
  }
}
