import { useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'
import { useSelector, useDispatch } from 'react-redux'
import { fetchOHLCV, setCurrentSymbol, setCurrentExchange, setCurrentTimeframe } from '../../store/slices/chartSlice'
import './Chart.css'

const Chart = () => {
  const { exchange: exchangeParam, symbol: symbolParam } = useParams()
  const dispatch = useDispatch()
  const { 
    currentSymbol, 
    currentExchange, 
    currentTimeframe, 
    availableTimeframes,
    loading 
  } = useSelector(state => state.chart)
  
  const [selectedExchange, setSelectedExchange] = useState(exchangeParam || 'binance')
  const [selectedSymbol, setSelectedSymbol] = useState(symbolParam || 'BTC/USDT')

  useEffect(() => {
    if (exchangeParam && symbolParam) {
      dispatch(setCurrentExchange(exchangeParam))
      dispatch(setCurrentSymbol(decodeURIComponent(symbolParam)))
      setSelectedExchange(exchangeParam)
      setSelectedSymbol(decodeURIComponent(symbolParam))
    }
  }, [exchangeParam, symbolParam, dispatch])

  useEffect(() => {
    if (selectedExchange && selectedSymbol) {
      dispatch(fetchOHLCV({
        exchangeId: selectedExchange,
        symbol: selectedSymbol,
        timeframe: currentTimeframe,
        limit: 100
      }))
    }
  }, [selectedExchange, selectedSymbol, currentTimeframe, dispatch])

  const handleTimeframeChange = (timeframe) => {
    dispatch(setCurrentTimeframe(timeframe))
  }

  const handleSymbolChange = (e) => {
    const newSymbol = e.target.value
    setSelectedSymbol(newSymbol)
    dispatch(setCurrentSymbol(newSymbol))
  }

  const handleExchangeChange = (e) => {
    const newExchange = e.target.value
    setSelectedExchange(newExchange)
    dispatch(setCurrentExchange(newExchange))
  }

  return (
    <div className="chart-page">
      <div className="chart-header">
        <div className="chart-controls">
          <div className="control-group">
            <label>Exchange:</label>
            <select value={selectedExchange} onChange={handleExchangeChange}>
              <option value="binance">Binance</option>
              <option value="okx">OKX</option>
              <option value="bybit">Bybit</option>
            </select>
          </div>
          
          <div className="control-group">
            <label>Symbol:</label>
            <input 
              type="text" 
              value={selectedSymbol} 
              onChange={handleSymbolChange}
              placeholder="BTC/USDT"
            />
          </div>
        </div>

        <div className="timeframe-selector">
          {availableTimeframes.map(tf => (
            <button
              key={tf}
              className={`timeframe-btn ${currentTimeframe === tf ? 'active' : ''}`}
              onClick={() => handleTimeframeChange(tf)}
            >
              {tf}
            </button>
          ))}
        </div>
      </div>

      <div className="chart-container">
        {loading ? (
          <div className="chart-loading">
            <div className="loading-spinner"></div>
            <p>Loading chart data...</p>
          </div>
        ) : (
          <div className="chart-placeholder">
            <div className="placeholder-content">
              <div className="placeholder-icon">📈</div>
              <h3>Chart Coming Soon</h3>
              <p>
                Trading chart for {selectedSymbol} on {selectedExchange}
                <br />
                Timeframe: {currentTimeframe}
              </p>
              <div className="chart-info">
                <p>This is where the Lightweight Charts component will be integrated.</p>
                <p>Features to be implemented:</p>
                <ul>
                  <li>Real-time candlestick charts</li>
                  <li>Technical indicators</li>
                  <li>Drawing tools</li>
                  <li>Volume analysis</li>
                  <li>Multi-timeframe support</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="chart-sidebar">
        <div className="sidebar-section">
          <h3>Market Info</h3>
          <div className="market-stats">
            <div className="stat-item">
              <span className="stat-label">Price</span>
              <span className="stat-value">$43,250.00</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">24h Change</span>
              <span className="stat-value positive">+2.45%</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">24h Volume</span>
              <span className="stat-value">1,234.56 BTC</span>
            </div>
          </div>
        </div>

        <div className="sidebar-section">
          <h3>Indicators</h3>
          <div className="indicators-list">
            <div className="indicator-item">
              <span>Moving Average (20)</span>
              <button className="indicator-toggle">+</button>
            </div>
            <div className="indicator-item">
              <span>RSI (14)</span>
              <button className="indicator-toggle">+</button>
            </div>
            <div className="indicator-item">
              <span>MACD</span>
              <button className="indicator-toggle">+</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Chart
