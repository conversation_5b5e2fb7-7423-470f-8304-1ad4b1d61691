import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001'

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// Market API endpoints
export const marketAPI = {
  // Get available exchanges
  getExchanges: () => api.get('/api/exchanges'),
  
  // Get markets for a specific exchange
  getMarkets: (exchangeId) => api.get(`/api/exchanges/${exchangeId}/markets`),
  
  // Get ticker data
  getTicker: (exchangeId, symbol) => api.get(`/api/exchanges/${exchangeId}/ticker/${symbol}`),
  
  // Get OHLCV data
  getOHLCV: (exchangeId, symbol, timeframe = '1m', limit = 100) => 
    api.get(`/api/exchanges/${exchangeId}/ohlcv/${symbol}`, {
      params: { timeframe, limit }
    }),
  
  // Search markets
  searchMarkets: (query, exchange = null, type = null) => 
    api.get('/api/search', {
      params: { query, exchange, type }
    }),
}

// WebSocket service
class WebSocketService {
  constructor() {
    this.socket = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 1000
    this.listeners = new Map()
  }

  connect() {
    if (this.socket?.connected) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      try {
        // Dynamic import for socket.io-client
        import('socket.io-client').then(({ io }) => {
          this.socket = io(API_BASE_URL, {
            transports: ['websocket', 'polling'],
            timeout: 5000,
          })

          this.socket.on('connect', () => {
            console.log('WebSocket connected')
            this.reconnectAttempts = 0
            resolve()
          })

          this.socket.on('disconnect', (reason) => {
            console.log('WebSocket disconnected:', reason)
            if (reason === 'io server disconnect') {
              // Server disconnected, try to reconnect
              this.reconnect()
            }
          })

          this.socket.on('connect_error', (error) => {
            console.error('WebSocket connection error:', error)
            reject(error)
          })

          // Handle real-time data
          this.socket.on('ticker_update', (data) => {
            this.emit('ticker_update', data)
          })

          this.socket.on('ohlcv_update', (data) => {
            this.emit('ohlcv_update', data)
          })
        })
      } catch (error) {
        reject(error)
      }
    })
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
    this.listeners.clear()
  }

  subscribe(exchange, symbol, timeframe) {
    if (this.socket?.connected) {
      this.socket.emit('subscribe', { exchange, symbol, timeframe })
    }
  }

  unsubscribe(exchange, symbol, timeframe) {
    if (this.socket?.connected) {
      this.socket.emit('unsubscribe', { exchange, symbol, timeframe })
    }
  }

  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    this.listeners.get(event).add(callback)
  }

  off(event, callback) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(callback)
    }
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => callback(data))
    }
  }

  reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached')
      return
    }

    this.reconnectAttempts++
    console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

    setTimeout(() => {
      this.connect().catch(() => {
        this.reconnect()
      })
    }, this.reconnectDelay * this.reconnectAttempts)
  }
}

export const wsService = new WebSocketService()

export default api
